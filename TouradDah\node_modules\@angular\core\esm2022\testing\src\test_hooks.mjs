/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Public Test Library for unit testing Angular applications. Assumes that you are running
 * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and
 * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.
 */
import { resetFakeAsyncZoneIfExists } from './fake_async';
import { TestBedImpl } from './test_bed';
// Reset the test providers and the fake async zone before each test.
// We keep a guard because somehow this file can make it into a bundle and be executed
// beforeEach is only defined when executing the tests
globalThis.beforeEach?.(getCleanupHook(false));
// We provide both a `beforeEach` and `afterEach`, because the updated behavior for
// tearing down the module is supposed to run after the test so that we can associate
// teardown errors with the correct test.
// We keep a guard because somehow this file can make it into a bundle and be executed
// afterEach is only defined when executing the tests
globalThis.afterEach?.(getCleanupHook(true));
function getCleanupHook(expectedTeardownValue) {
    return () => {
        const testBed = TestBedImpl.INSTANCE;
        if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {
            testBed.resetTestingModule();
            resetFakeAsyncZoneIfExists();
        }
    };
}
/**
 * This API should be removed. But doing so seems to break `google3` and so it requires a bit of
 * investigation.
 *
 * A work around is to mark it as `@codeGenApi` for now and investigate later.
 *
 * @codeGenApi
 */
// TODO(iminar): Remove this code in a safe way.
export const __core_private_testing_placeholder__ = '';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVzdF9ob29rcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvdGVzdGluZy9zcmMvdGVzdF9ob29rcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSDs7OztHQUlHO0FBRUgsT0FBTyxFQUFDLDBCQUEwQixFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQ3hELE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFFdkMscUVBQXFFO0FBQ3JFLHNGQUFzRjtBQUN0RixzREFBc0Q7QUFDdEQsVUFBVSxDQUFDLFVBQVUsRUFBRSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO0FBRS9DLG1GQUFtRjtBQUNuRixxRkFBcUY7QUFDckYseUNBQXlDO0FBQ3pDLHNGQUFzRjtBQUN0RixxREFBcUQ7QUFDckQsVUFBVSxDQUFDLFNBQVMsRUFBRSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO0FBRTdDLFNBQVMsY0FBYyxDQUFDLHFCQUE4QjtJQUNwRCxPQUFPLEdBQUcsRUFBRTtRQUNWLE1BQU0sT0FBTyxHQUFHLFdBQVcsQ0FBQyxRQUFRLENBQUM7UUFDckMsSUFBSSxPQUFPLENBQUMsMkJBQTJCLEVBQUUsS0FBSyxxQkFBcUIsRUFBRSxDQUFDO1lBQ3BFLE9BQU8sQ0FBQyxrQkFBa0IsRUFBRSxDQUFDO1lBQzdCLDBCQUEwQixFQUFFLENBQUM7UUFDL0IsQ0FBQztJQUNILENBQUMsQ0FBQztBQUNKLENBQUM7QUFFRDs7Ozs7OztHQU9HO0FBQ0gsZ0RBQWdEO0FBQ2hELE1BQU0sQ0FBQyxNQUFNLG9DQUFvQyxHQUFHLEVBQUUsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vKipcbiAqIFB1YmxpYyBUZXN0IExpYnJhcnkgZm9yIHVuaXQgdGVzdGluZyBBbmd1bGFyIGFwcGxpY2F0aW9ucy4gQXNzdW1lcyB0aGF0IHlvdSBhcmUgcnVubmluZ1xuICogd2l0aCBKYXNtaW5lLCBNb2NoYSwgb3IgYSBzaW1pbGFyIGZyYW1ld29yayB3aGljaCBleHBvcnRzIGEgYmVmb3JlRWFjaCBmdW5jdGlvbiBhbmRcbiAqIGFsbG93cyB0ZXN0cyB0byBiZSBhc3luY2hyb25vdXMgYnkgZWl0aGVyIHJldHVybmluZyBhIHByb21pc2Ugb3IgdXNpbmcgYSAnZG9uZScgcGFyYW1ldGVyLlxuICovXG5cbmltcG9ydCB7cmVzZXRGYWtlQXN5bmNab25lSWZFeGlzdHN9IGZyb20gJy4vZmFrZV9hc3luYyc7XG5pbXBvcnQge1Rlc3RCZWRJbXBsfSBmcm9tICcuL3Rlc3RfYmVkJztcblxuLy8gUmVzZXQgdGhlIHRlc3QgcHJvdmlkZXJzIGFuZCB0aGUgZmFrZSBhc3luYyB6b25lIGJlZm9yZSBlYWNoIHRlc3QuXG4vLyBXZSBrZWVwIGEgZ3VhcmQgYmVjYXVzZSBzb21laG93IHRoaXMgZmlsZSBjYW4gbWFrZSBpdCBpbnRvIGEgYnVuZGxlIGFuZCBiZSBleGVjdXRlZFxuLy8gYmVmb3JlRWFjaCBpcyBvbmx5IGRlZmluZWQgd2hlbiBleGVjdXRpbmcgdGhlIHRlc3RzXG5nbG9iYWxUaGlzLmJlZm9yZUVhY2g/LihnZXRDbGVhbnVwSG9vayhmYWxzZSkpO1xuXG4vLyBXZSBwcm92aWRlIGJvdGggYSBgYmVmb3JlRWFjaGAgYW5kIGBhZnRlckVhY2hgLCBiZWNhdXNlIHRoZSB1cGRhdGVkIGJlaGF2aW9yIGZvclxuLy8gdGVhcmluZyBkb3duIHRoZSBtb2R1bGUgaXMgc3VwcG9zZWQgdG8gcnVuIGFmdGVyIHRoZSB0ZXN0IHNvIHRoYXQgd2UgY2FuIGFzc29jaWF0ZVxuLy8gdGVhcmRvd24gZXJyb3JzIHdpdGggdGhlIGNvcnJlY3QgdGVzdC5cbi8vIFdlIGtlZXAgYSBndWFyZCBiZWNhdXNlIHNvbWVob3cgdGhpcyBmaWxlIGNhbiBtYWtlIGl0IGludG8gYSBidW5kbGUgYW5kIGJlIGV4ZWN1dGVkXG4vLyBhZnRlckVhY2ggaXMgb25seSBkZWZpbmVkIHdoZW4gZXhlY3V0aW5nIHRoZSB0ZXN0c1xuZ2xvYmFsVGhpcy5hZnRlckVhY2g/LihnZXRDbGVhbnVwSG9vayh0cnVlKSk7XG5cbmZ1bmN0aW9uIGdldENsZWFudXBIb29rKGV4cGVjdGVkVGVhcmRvd25WYWx1ZTogYm9vbGVhbikge1xuICByZXR1cm4gKCkgPT4ge1xuICAgIGNvbnN0IHRlc3RCZWQgPSBUZXN0QmVkSW1wbC5JTlNUQU5DRTtcbiAgICBpZiAodGVzdEJlZC5zaG91bGRUZWFyRG93blRlc3RpbmdNb2R1bGUoKSA9PT0gZXhwZWN0ZWRUZWFyZG93blZhbHVlKSB7XG4gICAgICB0ZXN0QmVkLnJlc2V0VGVzdGluZ01vZHVsZSgpO1xuICAgICAgcmVzZXRGYWtlQXN5bmNab25lSWZFeGlzdHMoKTtcbiAgICB9XG4gIH07XG59XG5cbi8qKlxuICogVGhpcyBBUEkgc2hvdWxkIGJlIHJlbW92ZWQuIEJ1dCBkb2luZyBzbyBzZWVtcyB0byBicmVhayBgZ29vZ2xlM2AgYW5kIHNvIGl0IHJlcXVpcmVzIGEgYml0IG9mXG4gKiBpbnZlc3RpZ2F0aW9uLlxuICpcbiAqIEEgd29yayBhcm91bmQgaXMgdG8gbWFyayBpdCBhcyBgQGNvZGVHZW5BcGlgIGZvciBub3cgYW5kIGludmVzdGlnYXRlIGxhdGVyLlxuICpcbiAqIEBjb2RlR2VuQXBpXG4gKi9cbi8vIFRPRE8oaW1pbmFyKTogUmVtb3ZlIHRoaXMgY29kZSBpbiBhIHNhZmUgd2F5LlxuZXhwb3J0IGNvbnN0IF9fY29yZV9wcml2YXRlX3Rlc3RpbmdfcGxhY2Vob2xkZXJfXyA9ICcnO1xuIl19