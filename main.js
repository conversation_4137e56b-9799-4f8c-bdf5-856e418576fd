// Menu mobile
let menuList = document.getElementById("menuList");
let isMenuOpen = false;

function toggleMenu() {
    if (!menuList) return;

    const menuIcon = document.querySelector('.menu-icon i');
    if (!menuIcon) return;

    if (!isMenuOpen) {
        menuList.classList.add('show');
        menuIcon.classList.replace('fa-bars', 'fa-times');
        isMenuOpen = true;
    } else {
        menuList.classList.remove('show');
        menuIcon.classList.replace('fa-times', 'fa-bars');
        isMenuOpen = false;
    }
}

// Fermer le menu quand on clique sur un lien et gérer la navigation smooth
document.addEventListener('DOMContentLoaded', () => {
    const menuLinks = document.querySelectorAll('#menuList a, nav a[href^="#"]');
    menuLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            // <PERSON>rmer le menu mobile si ouvert
            if(isMenuOpen) {
                toggleMenu();
            }

            // Gérer la navigation smooth pour les ancres
            const href = link.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80; // Offset pour la navbar fixe
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });

                    // Mettre à jour l'URL sans recharger la page
                    history.pushState(null, null, href);
                }
            }
        });
    });

    // Fermer le menu quand on clique en dehors
    document.addEventListener('click', (e) => {
        const navbar = document.querySelector('.navbar');
        if(isMenuOpen && !navbar.contains(e.target)) {
            toggleMenu();
        }
    });
});

// Fermer le menu mobile quand on redimensionne la fenêtre
window.addEventListener('resize', () => {
    if (window.innerWidth > 768 && isMenuOpen) {
        menuList.classList.remove('show');
        const menuIcon = document.querySelector('.menu-icon i');
        if (menuIcon) {
            menuIcon.classList.replace('fa-times', 'fa-bars');
        }
        isMenuOpen = false;
    }
});

// Gestion du thème
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('theme-icon');

    if (!themeIcon) return;

    const currentTheme = body.getAttribute('data-theme');

    if (currentTheme === 'dark') {
        body.removeAttribute('data-theme');
        themeIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'light');
    } else {
        body.setAttribute('data-theme', 'dark');
        themeIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'dark');
    }
}

function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeIcon = document.getElementById('theme-icon');

    if (!themeIcon) return;

    if (savedTheme === 'dark') {
        document.body.setAttribute('data-theme', 'dark');
        themeIcon.className = 'fas fa-moon';
    } else {
        themeIcon.className = 'fas fa-sun';
    }
}

// Scroll to top
function scrollToTop() {
    window.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth"
    });
}

function toggleScrollButton() {
    const scrollButton = document.getElementById('scrollToTop');
    if (scrollButton) {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        if (scrollTop > 300) {
            scrollButton.classList.add('show');

            // Mettre à jour la barre de progression circulaire
            const progressDegree = (scrollPercent / 100) * 360;
            scrollButton.style.setProperty('--progress', `${progressDegree}deg`);

            // Ajouter la classe progress si on a scrollé plus de 10%
            if (scrollPercent > 10) {
                scrollButton.classList.add('progress');
            } else {
                scrollButton.classList.remove('progress');
            }
        } else {
            scrollButton.classList.remove('show');
            scrollButton.classList.remove('progress');
        }
    }
}

// Animation au scroll avec Intersection Observer
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');

            // Animation spéciale pour les éléments de skills
            if (entry.target.classList.contains('skills-content')) {
                const skillCategories = entry.target.querySelectorAll('.skill-category');
                skillCategories.forEach((category, categoryIndex) => {
                    // Animer le titre de la catégorie
                    const title = category.querySelector('.category-title');
                    setTimeout(() => {
                        title.style.animation = `fadeInUp 0.8s ease-out forwards`;
                    }, categoryIndex * 200);

                    // Animer les items de la catégorie
                    const skillItems = category.querySelectorAll('.item');
                    skillItems.forEach((item, itemIndex) => {
                        setTimeout(() => {
                            item.style.animation = `scaleIn 0.6s ease-out forwards`;

                            // Animer la barre de progression
                            const progressBar = item.querySelector('.progress-bar');
                            if (progressBar) {
                                const percentage = progressBar.getAttribute('data-percentage');
                                setTimeout(() => {
                                    progressBar.style.width = percentage + '%';
                                }, 300);
                            }
                        }, (categoryIndex * 200) + 300 + (itemIndex * 100));
                    });
                });
            }

            // Animation spéciale pour les spécialités
            if (entry.target.classList.contains('specialties-section')) {
                const specialtyTitle = entry.target.querySelector('.specialties-title');
                const specialtyCards = entry.target.querySelectorAll('.specialty-card');

                // Animer le titre
                setTimeout(() => {
                    specialtyTitle.style.animation = `fadeInUp 0.8s ease-out forwards`;
                }, 200);

                // Animer les cartes
                specialtyCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = `fadeInUp 0.8s ease-out forwards`;
                    }, 600 + (index * 200));
                });
            }

            // Animation spéciale pour la section contact
            if (entry.target.id === 'contact') {
                const contactInfoLeft = entry.target.querySelector('.contact-info-left');
                const contactForm = entry.target.querySelector('.contact-form-container');
                const contactCards = entry.target.querySelectorAll('.contact-card');
                const socialLinks = entry.target.querySelectorAll('.social-link');

                // Animer la section info à gauche
                if (contactInfoLeft) {
                    setTimeout(() => {
                        contactInfoLeft.style.animation = `fadeInLeft 0.8s ease-out forwards`;
                    }, 200);
                }

                // Animer les cartes de contact
                contactCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = `fadeInLeft 0.6s ease-out forwards`;
                    }, 400 + (index * 150));
                });

                // Animer les liens sociaux
                socialLinks.forEach((link, index) => {
                    setTimeout(() => {
                        link.style.animation = `scaleIn 0.5s ease-out forwards`;
                    }, 800 + (index * 100));
                });

                // Animer le formulaire à droite
                if (contactForm) {
                    setTimeout(() => {
                        contactForm.style.animation = `fadeInRight 0.8s ease-out forwards`;
                    }, 600);
                }
            }

            // Animation spéciale pour la section portfolio
            if (entry.target.id === 'portfolio') {
                const portfolioFilters = entry.target.querySelector('.portfolio-filters');
                const portfolioItems = entry.target.querySelectorAll('.portfolio-item');

                // Animer les filtres
                if (portfolioFilters) {
                    setTimeout(() => {
                        portfolioFilters.style.animation = `fadeInUp 0.8s ease-out forwards`;
                    }, 200);
                }

                // Animer les items du portfolio
                portfolioItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('show');
                    }, 600 + (index * 200));
                });
            }

            // Animation spéciale pour les éléments de portfolio
            if (entry.target.classList.contains('portfolio-row')) {
                const portfolioItems = entry.target.querySelectorAll('.item');
                portfolioItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.style.animation = `fadeInUp 0.8s ease-out forwards`;
                    }, index * 200);
                });
            }
        }
    });
}, observerOptions);

// Effet machine à écrire
function typeWriter(element, text, speed = 100) {
    if (!element || !text) return;

    let i = 0;
    element.innerHTML = '';
    element.style.borderRight = '3px solid var(--secondar-color)';

    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        } else {
            setTimeout(() => {
                element.style.borderRight = 'none';
            }, 1000);
        }
    }
    type();
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Charger le thème
    loadTheme();

    // Observer les éléments à animer
    const animateElements = document.querySelectorAll('.animate-on-scroll');
    animateElements.forEach(element => {
        observer.observe(element);
    });

    // Effet machine à écrire
    setTimeout(() => {
        const nameElement = document.getElementById('typewriter-name');
        const titleElement = document.getElementById('typewriter-title');

        if (nameElement) {
            typeWriter(nameElement, 'Tourad Med Mahmoud Dah', 80);
        }

        setTimeout(() => {
            if (titleElement) {
                typeWriter(titleElement, 'Développeur Full-stack', 100);
            }
        }, 2500);
    }, 1000);

    // Bouton scroll
    const scrollButton = document.getElementById('scrollToTop');
    if (scrollButton) {
        scrollButton.addEventListener('click', scrollToTop);
    }
});

// Gestion du scroll
function handleScroll() {
    const scrolled = window.pageYOffset;

    // Effet de navbar au scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (scrolled > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }

    // Mise à jour de la barre de progression
    updateScrollProgress();

    // Gestion du bouton scroll to top
    toggleScrollButton();
}

// Fonction pour mettre à jour la barre de progression
function updateScrollProgress() {
    const scrollProgress = document.querySelector('.scroll-progress');
    if (!scrollProgress) return;

    // Calculer le pourcentage de défilement
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const docHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    // Mettre à jour la largeur de la barre
    scrollProgress.style.width = scrollPercent + '%';

    // Ajouter/retirer la classe visible selon le scroll
    if (scrollTop > 50) {
        scrollProgress.classList.add('visible');
    } else {
        scrollProgress.classList.remove('visible');
    }
}

window.addEventListener('scroll', handleScroll);

// Fonctionnalité de filtrage du portfolio
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Mettre à jour les boutons actifs
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filtrer les items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');

                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.classList.add('show');
                    }, 100);
                } else {
                    item.classList.remove('show');
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });

    // Initialiser tous les items comme visibles
    portfolioItems.forEach(item => {
        item.style.display = 'block';
    });
});



