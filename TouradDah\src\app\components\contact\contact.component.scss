.contact-section {
  padding: 100px 0;
  background: var(--bg-color);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
  }
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--gray);
  max-width: 600px;
  margin: 0 auto;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

// Informations de contact
.contact-info {
  .info-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
  }

  .info-description {
    color: var(--gray);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

.contact-methods {
  margin-bottom: 2rem;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    transform: translateX(5px);
  }
}

.method-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.method-info {
  h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.3rem;
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: var(--secondar-color);
    }
  }

  span {
    color: var(--gray);
  }
}

.social-links {
  display: flex;
  gap: 1rem;

  a {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(32, 201, 151, 0.3);
    }

    i {
      font-size: 18px;
    }
  }
}

// Formulaire de contact
.contact-form-container {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 0.5rem;
      font-size: 0.95rem;
    }

    .form-control {
      width: 100%;
      padding: 0.8rem 1rem;
      border: 2px solid var(--border-color);
      border-radius: 10px;
      background: var(--bg-color);
      color: var(--text-color);
      font-size: 1rem;
      transition: all 0.3s ease;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(32, 201, 151, 0.1);
      }

      &.error {
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
      }

      &::placeholder {
        color: var(--gray);
      }
    }

    textarea.form-control {
      resize: vertical;
      min-height: 120px;
    }

    .error-message {
      color: #e74c3c;
      font-size: 0.85rem;
      margin-top: 0.3rem;
      display: block;
    }
  }
}

.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  i {
    font-size: 16px;
  }
}

.form-status {
  margin-top: 1rem;
}

.status-message {
  padding: 1rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;

  &.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.3);
  }

  &.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
  }

  i {
    font-size: 18px;
  }
}

// Responsive
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
    gap: 0.8rem;
  }

  .method-info {
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .info-title {
    font-size: 1.7rem;
  }
}

@media (max-width: 480px) {
  .contact-section {
    padding: 60px 0;
  }

  .contact-form-container {
    padding: 1.5rem;
  }

  .contact-method {
    padding: 0.8rem;
  }

  .method-icon {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  .social-links a {
    width: 40px;
    height: 40px;

    i {
      font-size: 16px;
    }
  }

  .submit-btn {
    padding: 0.8rem 1.5rem;
  }
}
