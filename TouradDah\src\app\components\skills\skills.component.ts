export class SkillsComponent {
  skills = [
    { name: 'HTML', percentage: 90 },
    { name: 'CSS', percentage: 85 },
    { name: 'JavaScript', percentage: 80 },
    { name: 'Next.js', percentage: 75 },
    { name: 'Angular', percentage: 50 },
    { name: '<PERSON><PERSON><PERSON>', percentage: 85 },
    { name: '<PERSON>jan<PERSON>', percentage: 80 },
    { name: '<PERSON><PERSON>', percentage: 75 },
    { name: 'P<PERSON>', percentage: 80 },
    { name: 'Java', percentage: 70 },
    { name: 'MySQL', percentage: 80 },
    { name: 'PostgreSQL', percentage: 75 },
    { name: 'SQLite', percentage: 99 },
    { name: 'VS Code', percentage: 90 },
    { name: 'Git', percentage: 80 },
    { name: 'GitHub', percentage: 85 },
    { name: '<PERSON><PERSON>', percentage: 70 },
    { name: 'Eclipse', percentage: 75 },
    { name: 'Figma', percentage: 65 },
  ];

  constructor() {}

  getSkillProgress(skill) {
    return skill.percentage + '%';
  }
}