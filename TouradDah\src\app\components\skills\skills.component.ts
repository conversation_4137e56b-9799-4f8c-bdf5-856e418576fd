import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Skill {
  name: string;
  icon: string;
  level: number;
  category: string;
}

@Component({
  selector: 'app-skills',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './skills.component.html',
  styleUrls: ['./skills.component.scss']
})
export class SkillsComponent implements OnInit {
  
  skills: Skill[] = [
    // Frontend
    { name: 'HTML5', icon: 'assets/images/html.png', level: 95, category: 'frontend' },
    { name: 'CSS3', icon: 'assets/images/css.png', level: 90, category: 'frontend' },
    { name: 'JavaScript', icon: 'assets/images/js.png', level: 88, category: 'frontend' },
    { name: 'React', icon: 'assets/images/react.png', level: 85, category: 'frontend' },
    { name: 'Angular', icon: 'assets/images/angular.png', level: 80, category: 'frontend' },
    { name: 'Vue.js', icon: 'assets/images/vue.png', level: 75, category: 'frontend' },
    
    // Backend
    { name: 'Node.js', icon: 'assets/images/nodejs.png', level: 85, category: 'backend' },
    { name: 'Python', icon: 'assets/images/python.png', level: 90, category: 'backend' },
    { name: 'PHP', icon: 'assets/images/php.png', level: 80, category: 'backend' },
    { name: 'Java', icon: 'assets/images/java.png', level: 75, category: 'backend' },
    
    // Database
    { name: 'MySQL', icon: 'assets/images/mysql.png', level: 85, category: 'database' },
    { name: 'MongoDB', icon: 'assets/images/mongodb.png', level: 80, category: 'database' },
    { name: 'PostgreSQL', icon: 'assets/images/postgresql.png', level: 75, category: 'database' },
    
    // Tools
    { name: 'Git', icon: 'assets/images/git.png', level: 90, category: 'tools' },
    { name: 'Docker', icon: 'assets/images/docker.png', level: 70, category: 'tools' },
    { name: 'Linux', icon: 'assets/images/linux.png', level: 85, category: 'tools' }
  ];

  categories = [
    { id: 'all', name: 'Toutes', active: true },
    { id: 'frontend', name: 'Frontend', active: false },
    { id: 'backend', name: 'Backend', active: false },
    { id: 'database', name: 'Base de données', active: false },
    { id: 'tools', name: 'Outils', active: false }
  ];

  filteredSkills: Skill[] = [];
  activeCategory = 'all';

  ngOnInit() {
    this.filteredSkills = this.skills;
  }

  filterSkills(categoryId: string) {
    this.activeCategory = categoryId;
    
    // Update active category
    this.categories.forEach(cat => {
      cat.active = cat.id === categoryId;
    });

    // Filter skills
    if (categoryId === 'all') {
      this.filteredSkills = this.skills;
    } else {
      this.filteredSkills = this.skills.filter(skill => skill.category === categoryId);
    }
  }

  getSkillsByCategory(category: string): Skill[] {
    return this.skills.filter(skill => skill.category === category);
  }

  trackBySkill(index: number, skill: Skill): string {
    return skill.name;
  }
}
