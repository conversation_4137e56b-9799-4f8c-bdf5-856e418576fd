/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined } from '../../util/assert';
import { global } from '../../util/global';
import { setupFrameworkInjectorProfiler } from '../debug/framework_injector_profiler';
import { setProfiler } from '../profiler';
import { isSignal } from '../reactivity/api';
import { applyChanges } from './change_detection_utils';
import { getComponent, getContext, getDirectiveMetadata, getDirectives, getHostElement, getInjector, getListeners, getOwningComponent, getRootComponents } from './discovery_utils';
import { getDependenciesFromInjectable, getInjectorMetadata, getInjectorProviders, getInjectorResolutionPath } from './injector_discovery_utils';
/**
 * This file introduces series of globally accessible debug tools
 * to allow for the Angular debugging story to function.
 *
 * To see this in action run the following command:
 *
 *   bazel run //packages/core/test/bundling/todo:devserver
 *
 *  Then load `localhost:5432` and start using the console tools.
 */
/**
 * This value reflects the property on the window where the dev
 * tools are patched (window.ng).
 * */
export const GLOBAL_PUBLISH_EXPANDO_KEY = 'ng';
const globalUtilsFunctions = {
    /**
     * Warning: functions that start with `ɵ` are considered *INTERNAL* and should not be relied upon
     * in application's code. The contract of those functions might be changed in any release and/or a
     * function can be removed completely.
     */
    'ɵgetDependenciesFromInjectable': getDependenciesFromInjectable,
    'ɵgetInjectorProviders': getInjectorProviders,
    'ɵgetInjectorResolutionPath': getInjectorResolutionPath,
    'ɵgetInjectorMetadata': getInjectorMetadata,
    'ɵsetProfiler': setProfiler,
    'getDirectiveMetadata': getDirectiveMetadata,
    'getComponent': getComponent,
    'getContext': getContext,
    'getListeners': getListeners,
    'getOwningComponent': getOwningComponent,
    'getHostElement': getHostElement,
    'getInjector': getInjector,
    'getRootComponents': getRootComponents,
    'getDirectives': getDirectives,
    'applyChanges': applyChanges,
    'isSignal': isSignal,
};
let _published = false;
/**
 * Publishes a collection of default debug tools onto`window.ng`.
 *
 * These functions are available globally when Angular is in development
 * mode and are automatically stripped away from prod mode is on.
 */
export function publishDefaultGlobalUtils() {
    if (!_published) {
        _published = true;
        if (typeof window !== 'undefined') {
            // Only configure the injector profiler when running in the browser.
            setupFrameworkInjectorProfiler();
        }
        for (const [methodName, method] of Object.entries(globalUtilsFunctions)) {
            publishGlobalUtil(methodName, method);
        }
    }
}
/**
 * Publishes the given function to `window.ng` so that it can be
 * used from the browser console when an application is not in production.
 */
export function publishGlobalUtil(name, fn) {
    if (typeof COMPILED === 'undefined' || !COMPILED) {
        // Note: we can't export `ng` when using closure enhanced optimization as:
        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global
        // - we can't declare a closure extern as the namespace `ng` is already used within Google
        //   for typings for AngularJS (via `goog.provide('ng....')`).
        const w = global;
        ngDevMode && assertDefined(fn, 'function not defined');
        w[GLOBAL_PUBLISH_EXPANDO_KEY] ??= {};
        w[GLOBAL_PUBLISH_EXPANDO_KEY][name] = fn;
    }
}
//# sourceMappingURL=data:application/json;base64,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