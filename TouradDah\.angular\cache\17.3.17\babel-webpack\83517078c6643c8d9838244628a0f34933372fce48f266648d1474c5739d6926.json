{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function window(windowBoundaries) {\n  return operate((source, subscriber) => {\n    let windowSubject = new Subject();\n    subscriber.next(windowSubject.asObservable());\n    const errorHandler = err => {\n      windowSubject.error(err);\n      subscriber.error(err);\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value), () => {\n      windowSubject.complete();\n      subscriber.complete();\n    }, errorHandler));\n    innerFrom(windowBoundaries).subscribe(createOperatorSubscriber(subscriber, () => {\n      windowSubject.complete();\n      subscriber.next(windowSubject = new Subject());\n    }, noop, errorHandler));\n    return () => {\n      windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n      windowSubject = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "noop", "innerFrom", "window", "windowBoundaries", "source", "subscriber", "windowSubject", "next", "asObservable", "<PERSON><PERSON><PERSON><PERSON>", "err", "error", "subscribe", "value", "complete", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/Mywebsite/TouradDah/node_modules/rxjs/dist/esm/internal/operators/window.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function window(windowBoundaries) {\n    return operate((source, subscriber) => {\n        let windowSubject = new Subject();\n        subscriber.next(windowSubject.asObservable());\n        const errorHandler = (err) => {\n            windowSubject.error(err);\n            subscriber.error(err);\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value), () => {\n            windowSubject.complete();\n            subscriber.complete();\n        }, errorHandler));\n        innerFrom(windowBoundaries).subscribe(createOperatorSubscriber(subscriber, () => {\n            windowSubject.complete();\n            subscriber.next((windowSubject = new Subject()));\n        }, noop, errorHandler));\n        return () => {\n            windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n            windowSubject = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,MAAMA,CAACC,gBAAgB,EAAE;EACrC,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,aAAa,GAAG,IAAIT,OAAO,CAAC,CAAC;IACjCQ,UAAU,CAACE,IAAI,CAACD,aAAa,CAACE,YAAY,CAAC,CAAC,CAAC;IAC7C,MAAMC,YAAY,GAAIC,GAAG,IAAK;MAC1BJ,aAAa,CAACK,KAAK,CAACD,GAAG,CAAC;MACxBL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;IACzB,CAAC;IACDN,MAAM,CAACQ,SAAS,CAACb,wBAAwB,CAACM,UAAU,EAAGQ,KAAK,IAAKP,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,IAAI,CAACM,KAAK,CAAC,EAAE,MAAM;MAC5JP,aAAa,CAACQ,QAAQ,CAAC,CAAC;MACxBT,UAAU,CAACS,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEL,YAAY,CAAC,CAAC;IACjBR,SAAS,CAACE,gBAAgB,CAAC,CAACS,SAAS,CAACb,wBAAwB,CAACM,UAAU,EAAE,MAAM;MAC7EC,aAAa,CAACQ,QAAQ,CAAC,CAAC;MACxBT,UAAU,CAACE,IAAI,CAAED,aAAa,GAAG,IAAIT,OAAO,CAAC,CAAE,CAAC;IACpD,CAAC,EAAEG,IAAI,EAAES,YAAY,CAAC,CAAC;IACvB,OAAO,MAAM;MACTH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACS,WAAW,CAAC,CAAC;MACzFT,aAAa,GAAG,IAAI;IACxB,CAAC;EACL,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}