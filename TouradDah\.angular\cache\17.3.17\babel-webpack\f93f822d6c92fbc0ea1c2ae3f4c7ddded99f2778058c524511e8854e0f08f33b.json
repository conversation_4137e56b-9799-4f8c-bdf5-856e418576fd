{"ast": null, "code": "export const routes = [{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n}, {\n  path: 'home',\n  loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: 'about',\n  loadComponent: () => import('./components/about/about.component').then(m => m.AboutComponent)\n}, {\n  path: 'skills',\n  loadComponent: () => import('./components/skills/skills.component').then(m => m.SkillsComponent)\n}, {\n  path: 'portfolio',\n  loadComponent: () => import('./components/portfolio/portfolio.component').then(m => m.PortfolioComponent)\n}, {\n  path: 'education',\n  loadComponent: () => import('./components/education/education.component').then(m => m.EducationComponent)\n}, {\n  path: 'contact',\n  loadComponent: () => import('./components/contact/contact.component').then(m => m.ContactComponent)\n}, {\n  path: '**',\n  redirectTo: '/home'\n}];", "map": {"version": 3, "names": ["routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "HomeComponent", "AboutComponent", "SkillsComponent", "PortfolioComponent", "EducationComponent", "ContactComponent"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  { path: '', redirectTo: '/home', pathMatch: 'full' },\n  { path: 'home', loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent) },\n  { path: 'about', loadComponent: () => import('./components/about/about.component').then(m => m.AboutComponent) },\n  { path: 'skills', loadComponent: () => import('./components/skills/skills.component').then(m => m.SkillsComponent) },\n  { path: 'portfolio', loadComponent: () => import('./components/portfolio/portfolio.component').then(m => m.PortfolioComponent) },\n  { path: 'education', loadComponent: () => import('./components/education/education.component').then(m => m.EducationComponent) },\n  { path: 'contact', loadComponent: () => import('./components/contact/contact.component').then(m => m.ContactComponent) },\n  { path: '**', redirectTo: '/home' }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,MAAM;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;AAAC,CAAE,EAC5G;EAAEN,IAAI,EAAE,OAAO;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,cAAc;AAAC,CAAE,EAChH;EAAEP,IAAI,EAAE,QAAQ;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe;AAAC,CAAE,EACpH;EAAER,IAAI,EAAE,WAAW;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,kBAAkB;AAAC,CAAE,EAChI;EAAET,IAAI,EAAE,WAAW;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,kBAAkB;AAAC,CAAE,EAChI;EAAEV,IAAI,EAAE,SAAS;EAAEG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,gBAAgB;AAAC,CAAE,EACxH;EAAEX,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAO,CAAE,CACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}