/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertNumber } from '../../util/assert';
import { ID } from './view';
// Keeps track of the currently-active LViews.
const TRACKED_LVIEWS = new Map();
// Used for generating unique IDs for LViews.
let uniqueIdCounter = 0;
/** Gets a unique ID that can be assigned to an LView. */
export function getUniqueLViewId() {
    return uniqueIdCounter++;
}
/** Starts tracking an LView. */
export function registerLView(lView) {
    ngDevMode && assertNumber(lView[ID], 'LView must have an ID in order to be registered');
    TRACKED_LVIEWS.set(lView[ID], lView);
}
/** Gets an LView by its unique ID. */
export function getLViewById(id) {
    ngDevMode && assertNumber(id, 'ID used for LView lookup must be a number');
    return TRACKED_LVIEWS.get(id) || null;
}
/** Stops tracking an LView. */
export function unregisterLView(lView) {
    ngDevMode && assertNumber(lView[ID], 'Cannot stop tracking an LView that does not have an ID');
    TRACKED_LVIEWS.delete(lView[ID]);
}
//# sourceMappingURL=data:application/json;base64,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