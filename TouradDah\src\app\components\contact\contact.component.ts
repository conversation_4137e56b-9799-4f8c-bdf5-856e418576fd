import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <section id="contact" class="contact-section">
      <div class="container">
        <div class="section-header animate-on-scroll">
          <h2 class="section-title">Contactez-moi</h2>
          <p class="section-subtitle">N'hésitez pas à me contacter pour discuter de vos projets</p>
        </div>

        <div class="contact-content">
          <!-- Informations de contact -->
          <div class="contact-info animate-on-scroll">
            <h3 class="info-title">Restons en contact</h3>
            <p class="info-description">
              Je suis toujours ouvert aux nouvelles opportunités et collaborations. 
              Contactez-moi pour discuter de vos projets !
            </p>

            <div class="contact-methods">
              <div class="contact-method">
                <div class="method-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="method-info">
                  <h4>Email</h4>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <i class="fas fa-phone"></i>
                </div>
                <div class="method-info">
                  <h4>Téléphone</h4>
                  <a href="tel:+22238493149">+222 38 49 31 49</a>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="method-info">
                  <h4>Localisation</h4>
                  <span>Nouakchott, Mauritanie</span>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <i class="fab fa-whatsapp"></i>
                </div>
                <div class="method-info">
                  <h4>WhatsApp</h4>
                  <a href="http://wa.me/+22238493149" target="_blank" rel="noopener">
                    Envoyer un message
                  </a>
                </div>
              </div>
            </div>

            <div class="social-links">
              <a href="https://github.com/tourad" target="_blank" rel="noopener" title="GitHub">
                <i class="fa-brands fa-github"></i>
              </a>
              <a href="#" title="Facebook">
                <i class="fa-brands fa-square-facebook"></i>
              </a>
              <a href="#" target="_blank" rel="noopener" title="Twitter">
                <i class="fa-brands fa-square-x-twitter"></i>
              </a>
              <a href="#" title="LinkedIn">
                <i class="fa-brands fa-linkedin"></i>
              </a>
            </div>
          </div>

          <!-- Formulaire de contact -->
          <div class="contact-form-container animate-on-scroll">
            <form class="contact-form" (ngSubmit)="onSubmit()" #contactForm="ngForm">
              <div class="form-group">
                <label for="name">Nom complet *</label>
                <input 
                  type="text" 
                  id="name" 
                  name="name"
                  [(ngModel)]="formData.name"
                  required
                  #nameInput="ngModel"
                  class="form-control"
                  [class.error]="nameInput.invalid && nameInput.touched"
                  placeholder="Votre nom complet">
                <div class="error-message" *ngIf="nameInput.invalid && nameInput.touched">
                  Le nom est requis
                </div>
              </div>

              <div class="form-group">
                <label for="email">Email *</label>
                <input 
                  type="email" 
                  id="email" 
                  name="email"
                  [(ngModel)]="formData.email"
                  required
                  email
                  #emailInput="ngModel"
                  class="form-control"
                  [class.error]="emailInput.invalid && emailInput.touched"
                  placeholder="<EMAIL>">
                <div class="error-message" *ngIf="emailInput.invalid && emailInput.touched">
                  <span *ngIf="emailInput.errors?.['required']">L'email est requis</span>
                  <span *ngIf="emailInput.errors?.['email']">Format d'email invalide</span>
                </div>
              </div>

              <div class="form-group">
                <label for="subject">Sujet *</label>
                <input 
                  type="text" 
                  id="subject" 
                  name="subject"
                  [(ngModel)]="formData.subject"
                  required
                  #subjectInput="ngModel"
                  class="form-control"
                  [class.error]="subjectInput.invalid && subjectInput.touched"
                  placeholder="Sujet de votre message">
                <div class="error-message" *ngIf="subjectInput.invalid && subjectInput.touched">
                  Le sujet est requis
                </div>
              </div>

              <div class="form-group">
                <label for="message">Message *</label>
                <textarea 
                  id="message" 
                  name="message"
                  [(ngModel)]="formData.message"
                  required
                  #messageInput="ngModel"
                  class="form-control"
                  [class.error]="messageInput.invalid && messageInput.touched"
                  rows="6"
                  placeholder="Votre message..."></textarea>
                <div class="error-message" *ngIf="messageInput.invalid && messageInput.touched">
                  Le message est requis
                </div>
              </div>

              <button 
                type="submit" 
                class="submit-btn"
                [disabled]="contactForm.invalid || isSubmitting">
                <span *ngIf="!isSubmitting">
                  <i class="fas fa-paper-plane"></i>
                  Envoyer le message
                </span>
                <span *ngIf="isSubmitting">
                  <i class="fas fa-spinner fa-spin"></i>
                  Envoi en cours...
                </span>
              </button>
            </form>

            <div class="form-status" *ngIf="submitStatus">
              <div class="status-message" [class]="submitStatus.type">
                <i [class]="submitStatus.icon"></i>
                {{ submitStatus.message }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  `,
  styleUrls: ['./contact.component.scss']
})
export class ContactComponent {
  
  formData: ContactForm = {
    name: '',
    email: '',
    subject: '',
    message: ''
  };

  isSubmitting = false;
  submitStatus: { type: string; message: string; icon: string } | null = null;

  onSubmit() {
    if (this.isSubmitting) return;

    this.isSubmitting = true;
    this.submitStatus = null;

    // Simulation d'envoi (remplacer par vraie logique d'envoi)
    setTimeout(() => {
      this.isSubmitting = false;
      this.submitStatus = {
        type: 'success',
        message: 'Message envoyé avec succès ! Je vous répondrai bientôt.',
        icon: 'fas fa-check-circle'
      };

      // Reset form
      this.formData = {
        name: '',
        email: '',
        subject: '',
        message: ''
      };

      // Clear status after 5 seconds
      setTimeout(() => {
        this.submitStatus = null;
      }, 5000);
    }, 2000);
  }
}
