/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Below are constants for LView indices to help us look up LView members
// without having to remember the specific indices.
// Uglify will inline these when minifying so there shouldn't be a cost.
export const HOST = 0;
export const TVIEW = 1;
// Shared with LContainer
export const FLAGS = 2;
export const PARENT = 3;
export const NEXT = 4;
export const T_HOST = 5;
// End shared with LContainer
export const HYDRATION = 6;
export const CLEANUP = 7;
export const CONTEXT = 8;
export const INJECTOR = 9;
export const ENVIRONMENT = 10;
export const RENDERER = 11;
export const CHILD_HEAD = 12;
export const CHILD_TAIL = 13;
// FIXME(misko): Investigate if the three declarations aren't all same thing.
export const DECLARATION_VIEW = 14;
export const DECLARATION_COMPONENT_VIEW = 15;
export const DECLARATION_LCONTAINER = 16;
export const PREORDER_HOOK_FLAGS = 17;
export const QUERIES = 18;
export const ID = 19;
export const EMBEDDED_VIEW_INJECTOR = 20;
export const ON_DESTROY_HOOKS = 21;
export const EFFECTS_TO_SCHEDULE = 22;
export const REACTIVE_TEMPLATE_CONSUMER = 23;
/**
 * Size of LView's header. Necessary to adjust for it when setting slots.
 *
 * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate
 * instruction index into `LView` index. All other indexes should be in the `LView` index space and
 * there should be no need to refer to `HEADER_OFFSET` anywhere else.
 */
export const HEADER_OFFSET = 25;
//# sourceMappingURL=data:application/json;base64,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