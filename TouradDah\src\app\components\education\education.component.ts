import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Education {
  id: number;
  degree: string;
  institution: string;
  period: string;
  description: string;
  icon: string;
}

interface Experience {
  id: number;
  position: string;
  company: string;
  period: string;
  description: string;
  technologies: string[];
}

@Component({
  selector: 'app-education',
  standalone: true,
  imports: [CommonModule],
  template: `
    <section id="education" class="education-section">
      <div class="container">
        <div class="section-header animate-on-scroll">
          <h2 class="section-title">Formation & Expérience</h2>
          <p class="section-subtitle">Mon parcours académique et professionnel</p>
        </div>

        <div class="education-content">
          <!-- Section Formation -->
          <div class="education-block animate-on-scroll">
            <h3 class="block-title">
              <i class="fas fa-graduation-cap"></i>
              Formation
            </h3>
            
            <div class="timeline">
              <div 
                *ngFor="let edu of education; let i = index"
                class="timeline-item"
                [class.left]="i % 2 === 0"
                [class.right]="i % 2 !== 0">
                
                <div class="timeline-content">
                  <div class="timeline-icon">
                    <i [class]="edu.icon"></i>
                  </div>
                  
                  <div class="timeline-card">
                    <div class="card-header">
                      <h4 class="timeline-title">{{ edu.degree }}</h4>
                      <span class="timeline-period">{{ edu.period }}</span>
                    </div>
                    
                    <div class="timeline-institution">{{ edu.institution }}</div>
                    <p class="timeline-description">{{ edu.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Section Expérience -->
          <div class="experience-block animate-on-scroll">
            <h3 class="block-title">
              <i class="fas fa-briefcase"></i>
              Expérience Professionnelle
            </h3>
            
            <div class="experience-list">
              <div 
                *ngFor="let exp of experience"
                class="experience-item">
                
                <div class="experience-header">
                  <div class="experience-info">
                    <h4 class="experience-position">{{ exp.position }}</h4>
                    <div class="experience-company">{{ exp.company }}</div>
                    <div class="experience-period">{{ exp.period }}</div>
                  </div>
                </div>
                
                <p class="experience-description">{{ exp.description }}</p>
                
                <div class="experience-technologies">
                  <span 
                    *ngFor="let tech of exp.technologies"
                    class="tech-badge">
                    {{ tech }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  `,
  styleUrls: ['./education.component.scss']
})
export class EducationComponent {
  
  education: Education[] = [
    {
      id: 1,
      degree: 'Master en Informatique',
      institution: 'Université de Nouakchott',
      period: '2020 - 2022',
      description: 'Spécialisation en développement web et cybersécurité avec focus sur les technologies modernes.',
      icon: 'fas fa-university'
    },
    {
      id: 2,
      degree: 'Licence en Informatique',
      institution: 'Université de Nouakchott',
      period: '2017 - 2020',
      description: 'Formation générale en informatique couvrant programmation, bases de données et réseaux.',
      icon: 'fas fa-graduation-cap'
    },
    {
      id: 3,
      degree: 'Baccalauréat Scientifique',
      institution: 'Lycée de Nouakchott',
      period: '2016 - 2017',
      description: 'Baccalauréat série Sciences Mathématiques avec mention Bien.',
      icon: 'fas fa-school'
    }
  ];

  experience: Experience[] = [
    {
      id: 1,
      position: 'Développeur Full-stack',
      company: 'TechSolutions Mauritanie',
      period: '2022 - Présent',
      description: 'Développement d\'applications web complètes, gestion de projets et encadrement d\'équipes junior.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Docker', 'AWS']
    },
    {
      id: 2,
      position: 'Développeur Frontend',
      company: 'Digital Agency MR',
      period: '2021 - 2022',
      description: 'Création d\'interfaces utilisateur modernes et responsives pour diverses applications web.',
      technologies: ['Vue.js', 'JavaScript', 'CSS3', 'Figma', 'Git']
    },
    {
      id: 3,
      position: 'Stagiaire Développeur',
      company: 'StartupTech',
      period: '2020 - 2021',
      description: 'Apprentissage des bonnes pratiques de développement et contribution aux projets internes.',
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'PHP', 'MySQL']
    }
  ];
}
