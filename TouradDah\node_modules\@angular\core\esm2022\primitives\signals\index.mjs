/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { createComputed } from './src/computed';
export { defaultEquals } from './src/equality';
export { setThrowInvalidWriteToSignalError } from './src/errors';
export { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, getActiveConsumer, isInNotificationPhase, isReactive, producerAccessed, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, setActiveConsumer } from './src/graph';
export { SIGNAL_NODE, createSignal, setPostSignalSetFn, signalSetFn, signalUpdateFn } from './src/signal';
export { createWatch } from './src/watch';
export { setAlternateWeakRefImpl } from './src/weak_ref';
//# sourceMappingURL=data:application/json;base64,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