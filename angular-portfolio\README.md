# Angular Portfolio

This is an Angular-based portfolio project showcasing the skills, projects, and background of <PERSON>ad <PERSON>, a full-stack developer specializing in cybersecurity and modern web development.

## Project Structure

The project is organized into several components, each responsible for a specific section of the portfolio:

- **Header**: Contains the navigation menu and logo.
- **About**: Displays personal information and background.
- **Skills**: Showcases various skills and their proficiency levels.
- **Portfolio**: Displays a grid of projects.
- **Education**: Showcases educational qualifications.
- **Contact**: Includes a contact form for inquiries.
- **Footer**: Displays social media links and copyright information.

## Getting Started

To run this project locally, follow these steps:

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd angular-portfolio
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run the application**:
   ```bash
   ng serve
   ```

4. **Open your browser** and navigate to `http://localhost:4200`.

## Technologies Used

- Angular
- TypeScript
- HTML5
- CSS3
- Bootstrap (optional for styling)

## Features

- Responsive design
- Dynamic content loading
- Smooth scrolling navigation
- Theme toggling functionality

## Author

<PERSON><PERSON>  
Email: <EMAIL>  
Location: Nouakchott, Mauritanie

## License

This project is licensed under the MIT License.