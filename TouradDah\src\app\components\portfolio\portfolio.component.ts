import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Project {
  id: number;
  title: string;
  description: string;
  image: string;
  technologies: string[];
  category: string;
  demoUrl?: string;
  githubUrl?: string;
}

@Component({
  selector: 'app-portfolio',
  standalone: true,
  imports: [CommonModule],
  template: `
    <section id="portfolio" class="portfolio-section">
      <div class="container">
        <div class="section-header animate-on-scroll">
          <h2 class="section-title">Mes Projets</h2>
          <p class="section-subtitle">Découvrez quelques-uns de mes projets récents</p>
        </div>

        <!-- Filtres de catégories -->
        <div class="category-filters animate-on-scroll">
          <button 
            *ngFor="let category of categories"
            class="filter-btn"
            [class.active]="category.active"
            (click)="filterProjects(category.id)">
            {{ category.name }}
          </button>
        </div>

        <!-- Grille des projets -->
        <div class="projects-grid animate-on-scroll">
          <div 
            *ngFor="let project of filteredProjects; trackBy: trackByProject"
            class="project-card"
            [attr.data-category]="project.category">
            
            <div class="project-image">
              <img [src]="project.image" [alt]="project.title">
              <div class="project-overlay">
                <div class="project-actions">
                  <a 
                    *ngIf="project.demoUrl" 
                    [href]="project.demoUrl" 
                    target="_blank" 
                    rel="noopener"
                    class="action-btn demo-btn"
                    title="Voir la démo">
                    <i class="fas fa-external-link-alt"></i>
                  </a>
                  <a 
                    *ngIf="project.githubUrl" 
                    [href]="project.githubUrl" 
                    target="_blank" 
                    rel="noopener"
                    class="action-btn github-btn"
                    title="Voir le code">
                    <i class="fab fa-github"></i>
                  </a>
                </div>
              </div>
            </div>

            <div class="project-content">
              <h3 class="project-title">{{ project.title }}</h3>
              <p class="project-description">{{ project.description }}</p>
              
              <div class="project-technologies">
                <span 
                  *ngFor="let tech of project.technologies"
                  class="tech-tag">
                  {{ tech }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  `,
  styleUrls: ['./portfolio.component.scss']
})
export class PortfolioComponent {
  
  projects: Project[] = [
    {
      id: 1,
      title: 'E-commerce Platform',
      description: 'Plateforme e-commerce complète avec gestion des produits, panier et paiement sécurisé.',
      image: 'assets/images/project1.jpg',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      category: 'web',
      demoUrl: '#',
      githubUrl: '#'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'Application de gestion de tâches avec collaboration en temps réel.',
      image: 'assets/images/project2.jpg',
      technologies: ['Angular', 'Firebase', 'TypeScript'],
      category: 'web',
      demoUrl: '#',
      githubUrl: '#'
    },
    {
      id: 3,
      title: 'Security Scanner',
      description: 'Outil de scan de sécurité pour détecter les vulnérabilités web.',
      image: 'assets/images/project3.jpg',
      technologies: ['Python', 'Flask', 'SQLite'],
      category: 'security',
      githubUrl: '#'
    },
    {
      id: 4,
      title: 'Mobile Banking App',
      description: 'Application mobile de banque avec authentification biométrique.',
      image: 'assets/images/project4.jpg',
      technologies: ['React Native', 'Node.js', 'PostgreSQL'],
      category: 'mobile',
      demoUrl: '#'
    },
    {
      id: 5,
      title: 'Portfolio Website',
      description: 'Site portfolio responsive avec animations et mode sombre.',
      image: 'assets/images/project5.jpg',
      technologies: ['HTML5', 'CSS3', 'JavaScript'],
      category: 'web',
      demoUrl: '#',
      githubUrl: '#'
    },
    {
      id: 6,
      title: 'API REST',
      description: 'API REST complète avec authentification JWT et documentation.',
      image: 'assets/images/project6.jpg',
      technologies: ['Node.js', 'Express', 'MongoDB', 'JWT'],
      category: 'backend',
      githubUrl: '#'
    }
  ];

  categories = [
    { id: 'all', name: 'Tous', active: true },
    { id: 'web', name: 'Web', active: false },
    { id: 'mobile', name: 'Mobile', active: false },
    { id: 'security', name: 'Sécurité', active: false },
    { id: 'backend', name: 'Backend', active: false }
  ];

  filteredProjects: Project[] = [];
  activeCategory = 'all';

  constructor() {
    this.filteredProjects = this.projects;
  }

  filterProjects(categoryId: string) {
    this.activeCategory = categoryId;
    
    // Update active category
    this.categories.forEach(cat => {
      cat.active = cat.id === categoryId;
    });

    // Filter projects
    if (categoryId === 'all') {
      this.filteredProjects = this.projects;
    } else {
      this.filteredProjects = this.projects.filter(project => project.category === categoryId);
    }
  }

  trackByProject(index: number, project: Project): number {
    return project.id;
  }
}
