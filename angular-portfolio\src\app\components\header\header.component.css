.header {
    background-color: #333;
    color: #fff;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar {
    display: flex;
    align-items: center;
}

#logo {
    font-size: 24px;
    font-weight: bold;
    text-decoration: none;
    color: #fff;
}

#menuList {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

#menuList li {
    margin: 0 15px;
}

#menuList a {
    text-decoration: none;
    color: #fff;
    transition: color 0.3s;
}

#menuList a:hover {
    color: #ffcc00;
}

.nav-controls {
    display: flex;
    align-items: center;
}

.theme-toggle {
    cursor: pointer;
    margin-right: 15px;
}

.menu-icon {
    cursor: pointer;
}