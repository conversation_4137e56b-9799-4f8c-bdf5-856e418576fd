/* Styles specific to the contact component */

.contact-layout {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
}

.contact-info-left {
    flex: 1;
    margin-right: 20px;
}

.contact-description {
    font-size: 16px;
    margin-bottom: 20px;
}

.contact-cards {
    margin-bottom: 20px;
}

.contact-card {
    margin-bottom: 15px;
}

.contact-social {
    margin-top: 20px;
}

.contact-form-container {
    flex: 1;
}

.contact-form {
    display: flex;
    flex-direction: column;
}

.form-group {
    margin-bottom: 15px;
}

.form-group input,
.form-group textarea {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    width: 100%;
}

.form-group button {
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.form-group button:hover {
    background-color: #0056b3;
}