/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Represents an instance of an `NgModule` created by an `NgModuleFactory`.
 * Provides access to the `NgModule` instance and related objects.
 *
 * @publicApi
 */
export class NgModuleRef {
}
/**
 * @publicApi
 *
 * @deprecated
 * This class was mostly used as a part of ViewEngine-based JIT API and is no longer needed in Ivy
 * JIT mode. See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes)
 * for additional context. Angular provides APIs that accept NgModule classes directly (such as
 * [PlatformRef.bootstrapModule](api/core/PlatformRef#bootstrapModule) and
 * [createNgModule](api/core/createNgModule)), consider switching to those APIs instead of
 * using factory-based ones.
 */
export class NgModuleFactory {
}
//# sourceMappingURL=data:application/json;base64,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