body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    color: #333;
}

h1, h2, h3, h4 {
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
    color: inherit;
}

.container {
    width: 80%;
    margin: auto;
    overflow: hidden;
}

header {
    background: #333;
    color: #fff;
    padding: 20px 0;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar a {
    color: #fff;
    padding: 10px 15px;
}

.header-content {
    text-align: center;
    padding: 50px 0;
}

.header-content h1 {
    font-size: 2.5em;
}

.header-content p {
    font-size: 1.2em;
}

.skills-highlight {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.skill-badge {
    background: #f4f4f4;
    border-radius: 5px;
    padding: 10px;
    margin: 0 10px;
}

footer {
    background: #333;
    color: #fff;
    text-align: center;
    padding: 10px 0;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.portfolio-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
    transition: transform 0.3s;
}

.portfolio-card:hover {
    transform: scale(1.05);
}

.portfolio-image img {
    width: 100%;
    height: auto;
}

.portfolio-content {
    padding: 15px;
}

.portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.portfolio-tech {
    margin-top: 10px;
}

.tech-tag {
    background: #007bff;
    color: #fff;
    padding: 5px;
    border-radius: 3px;
    margin-right: 5px;
}

.contact-form {
    display: flex;
    flex-direction: column;
}

.contact-form .form-group {
    margin-bottom: 15px;
}

.contact-form input, 
.contact-form textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.contact-form button {
    padding: 10px;
    background: #007bff;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.contact-form button:hover {
    background: #0056b3;
}