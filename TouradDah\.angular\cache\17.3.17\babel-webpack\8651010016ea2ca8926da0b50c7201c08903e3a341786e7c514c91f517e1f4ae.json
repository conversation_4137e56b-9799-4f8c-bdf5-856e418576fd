{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction EducationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 18)(5, \"div\", 19)(6, \"h4\", 20);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 21);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 22);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const edu_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵclassProp(\"left\", i_r2 % 2 === 0)(\"right\", i_r2 % 2 !== 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(edu_r1.icon);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(edu_r1.degree);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(edu_r1.period);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(edu_r1.institution);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(edu_r1.description);\n  }\n}\nfunction EducationComponent_div_19_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tech_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tech_r3, \" \");\n  }\n}\nfunction EducationComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"h4\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"p\", 30);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 31);\n    i0.ɵɵtemplate(12, EducationComponent_div_19_span_12_Template, 2, 1, \"span\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const exp_r4 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(exp_r4.position);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(exp_r4.company);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(exp_r4.period);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(exp_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", exp_r4.technologies);\n  }\n}\nexport class EducationComponent {\n  constructor() {\n    this.education = [{\n      id: 1,\n      degree: 'Master en Informatique',\n      institution: 'Université de Nouakchott',\n      period: '2020 - 2022',\n      description: 'Spécialisation en développement web et cybersécurité avec focus sur les technologies modernes.',\n      icon: 'fas fa-university'\n    }, {\n      id: 2,\n      degree: 'Licence en Informatique',\n      institution: 'Université de Nouakchott',\n      period: '2017 - 2020',\n      description: 'Formation générale en informatique couvrant programmation, bases de données et réseaux.',\n      icon: 'fas fa-graduation-cap'\n    }, {\n      id: 3,\n      degree: 'Baccalauréat Scientifique',\n      institution: 'Lycée de Nouakchott',\n      period: '2016 - 2017',\n      description: 'Baccalauréat série Sciences Mathématiques avec mention Bien.',\n      icon: 'fas fa-school'\n    }];\n    this.experience = [{\n      id: 1,\n      position: 'Développeur Full-stack',\n      company: 'TechSolutions Mauritanie',\n      period: '2022 - Présent',\n      description: 'Développement d\\'applications web complètes, gestion de projets et encadrement d\\'équipes junior.',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Docker', 'AWS']\n    }, {\n      id: 2,\n      position: 'Développeur Frontend',\n      company: 'Digital Agency MR',\n      period: '2021 - 2022',\n      description: 'Création d\\'interfaces utilisateur modernes et responsives pour diverses applications web.',\n      technologies: ['Vue.js', 'JavaScript', 'CSS3', 'Figma', 'Git']\n    }, {\n      id: 3,\n      position: 'Stagiaire Développeur',\n      company: 'StartupTech',\n      period: '2020 - 2021',\n      description: 'Apprentissage des bonnes pratiques de développement et contribution aux projets internes.',\n      technologies: ['HTML5', 'CSS3', 'JavaScript', 'PHP', 'MySQL']\n    }];\n  }\n  static {\n    this.ɵfac = function EducationComponent_Factory(t) {\n      return new (t || EducationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EducationComponent,\n      selectors: [[\"app-education\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 2,\n      consts: [[\"id\", \"education\", 1, \"education-section\"], [1, \"container\"], [1, \"section-header\", \"animate-on-scroll\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"education-content\"], [1, \"education-block\", \"animate-on-scroll\"], [1, \"block-title\"], [1, \"fas\", \"fa-graduation-cap\"], [1, \"timeline\"], [\"class\", \"timeline-item\", 3, \"left\", \"right\", 4, \"ngFor\", \"ngForOf\"], [1, \"experience-block\", \"animate-on-scroll\"], [1, \"fas\", \"fa-briefcase\"], [1, \"experience-list\"], [\"class\", \"experience-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"timeline-item\"], [1, \"timeline-content\"], [1, \"timeline-icon\"], [1, \"timeline-card\"], [1, \"card-header\"], [1, \"timeline-title\"], [1, \"timeline-period\"], [1, \"timeline-institution\"], [1, \"timeline-description\"], [1, \"experience-item\"], [1, \"experience-header\"], [1, \"experience-info\"], [1, \"experience-position\"], [1, \"experience-company\"], [1, \"experience-period\"], [1, \"experience-description\"], [1, \"experience-technologies\"], [\"class\", \"tech-badge\", 4, \"ngFor\", \"ngForOf\"], [1, \"tech-badge\"]],\n      template: function EducationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Formation & Exp\\u00E9rience\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Mon parcours acad\\u00E9mique et professionnel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"h3\", 7);\n          i0.ɵɵelement(10, \"i\", 8);\n          i0.ɵɵtext(11, \" Formation \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 9);\n          i0.ɵɵtemplate(13, EducationComponent_div_13_Template, 14, 10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"h3\", 7);\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵtext(17, \" Exp\\u00E9rience Professionnelle \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 13);\n          i0.ɵɵtemplate(19, EducationComponent_div_19_Template, 13, 5, \"div\", 14);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.education);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.experience);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf],\n      styles: [\".education-section[_ngcontent-%COMP%] {\\n  padding: 100px 0;\\n  background: var(--bg-primary);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 4rem;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n  position: relative;\\n}\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: var(--gradient-primary);\\n  border-radius: 2px;\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: var(--gray);\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.education-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 4rem;\\n}\\n\\n.block-title[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 2rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.8rem;\\n}\\n.block-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.5rem;\\n}\\n\\n.timeline[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 2rem 0;\\n}\\n.timeline[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 50%;\\n  top: 0;\\n  bottom: 0;\\n  width: 2px;\\n  background: var(--primary-color);\\n  transform: translateX(-50%);\\n}\\n\\n.timeline-item[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 3rem;\\n}\\n.timeline-item.left[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%] {\\n  flex-direction: row;\\n  text-align: right;\\n}\\n.timeline-item.left[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-card[_ngcontent-%COMP%] {\\n  margin-right: 2rem;\\n}\\n.timeline-item.right[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%] {\\n  flex-direction: row-reverse;\\n  text-align: left;\\n}\\n.timeline-item.right[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-card[_ngcontent-%COMP%] {\\n  margin-left: 2rem;\\n}\\n\\n.timeline-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.timeline-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  background: var(--primary-color);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 18px;\\n  position: relative;\\n  z-index: 2;\\n  box-shadow: 0 0 0 4px var(--bg-primary);\\n}\\n\\n.timeline-card[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  max-width: 300px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.timeline-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\\n  border-color: var(--primary-color);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0.8rem;\\n}\\n\\n.timeline-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 0.3rem;\\n}\\n\\n.timeline-period[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n  padding: 0.2rem 0.8rem;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n\\n.timeline-institution[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  margin-bottom: 0.8rem;\\n}\\n\\n.timeline-description[_ngcontent-%COMP%] {\\n  color: var(--gray);\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n}\\n\\n.experience-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2rem;\\n}\\n\\n.experience-item[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.experience-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: var(--gradient-primary);\\n}\\n.experience-item[_ngcontent-%COMP%]:hover {\\n  transform: translateX(5px);\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  border-color: var(--primary-color);\\n}\\n\\n.experience-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.experience-position[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 0.3rem;\\n}\\n\\n.experience-company[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  font-size: 1.1rem;\\n  margin-bottom: 0.3rem;\\n}\\n\\n.experience-period[_ngcontent-%COMP%] {\\n  color: var(--gray);\\n  font-size: 0.9rem;\\n  font-style: italic;\\n}\\n\\n.experience-description[_ngcontent-%COMP%] {\\n  color: var(--gray);\\n  line-height: 1.6;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.experience-technologies[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n\\n.tech-badge[_ngcontent-%COMP%] {\\n  background: var(--bg-primary);\\n  color: var(--primary-color);\\n  padding: 0.3rem 0.8rem;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  border: 1px solid var(--primary-color);\\n  transition: all 0.3s ease;\\n}\\n.tech-badge[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .education-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 3rem;\\n  }\\n  .timeline[_ngcontent-%COMP%]::before {\\n    left: 30px;\\n  }\\n  .timeline-item.left[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%], .timeline-item.right[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n  }\\n  .timeline-item.left[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-card[_ngcontent-%COMP%], .timeline-item.right[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-card[_ngcontent-%COMP%] {\\n    margin-left: 2rem;\\n    margin-right: 0;\\n  }\\n  .timeline-icon[_ngcontent-%COMP%] {\\n    position: absolute;\\n    left: 5px;\\n  }\\n  .timeline-card[_ngcontent-%COMP%] {\\n    max-width: none;\\n    margin-left: 4rem !important;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .education-section[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .timeline-card[_ngcontent-%COMP%], .experience-item[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .timeline-title[_ngcontent-%COMP%], .experience-position[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .block-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .timeline-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵclassProp", "i_r2", "ɵɵadvance", "ɵɵclassMap", "edu_r1", "icon", "ɵɵtextInterpolate", "degree", "period", "institution", "description", "ɵɵtextInterpolate1", "tech_r3", "ɵɵtemplate", "EducationComponent_div_19_span_12_Template", "exp_r4", "position", "company", "ɵɵproperty", "technologies", "EducationComponent", "constructor", "education", "id", "experience", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EducationComponent_Template", "rf", "ctx", "EducationComponent_div_13_Template", "EducationComponent_div_19_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\education\\education.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\ninterface Education {\n  id: number;\n  degree: string;\n  institution: string;\n  period: string;\n  description: string;\n  icon: string;\n}\n\ninterface Experience {\n  id: number;\n  position: string;\n  company: string;\n  period: string;\n  description: string;\n  technologies: string[];\n}\n\n@Component({\n  selector: 'app-education',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <section id=\"education\" class=\"education-section\">\n      <div class=\"container\">\n        <div class=\"section-header animate-on-scroll\">\n          <h2 class=\"section-title\">Formation & Expérience</h2>\n          <p class=\"section-subtitle\">Mon parcours académique et professionnel</p>\n        </div>\n\n        <div class=\"education-content\">\n          <!-- Section Formation -->\n          <div class=\"education-block animate-on-scroll\">\n            <h3 class=\"block-title\">\n              <i class=\"fas fa-graduation-cap\"></i>\n              Formation\n            </h3>\n            \n            <div class=\"timeline\">\n              <div \n                *ngFor=\"let edu of education; let i = index\"\n                class=\"timeline-item\"\n                [class.left]=\"i % 2 === 0\"\n                [class.right]=\"i % 2 !== 0\">\n                \n                <div class=\"timeline-content\">\n                  <div class=\"timeline-icon\">\n                    <i [class]=\"edu.icon\"></i>\n                  </div>\n                  \n                  <div class=\"timeline-card\">\n                    <div class=\"card-header\">\n                      <h4 class=\"timeline-title\">{{ edu.degree }}</h4>\n                      <span class=\"timeline-period\">{{ edu.period }}</span>\n                    </div>\n                    \n                    <div class=\"timeline-institution\">{{ edu.institution }}</div>\n                    <p class=\"timeline-description\">{{ edu.description }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Section Expérience -->\n          <div class=\"experience-block animate-on-scroll\">\n            <h3 class=\"block-title\">\n              <i class=\"fas fa-briefcase\"></i>\n              Expérience Professionnelle\n            </h3>\n            \n            <div class=\"experience-list\">\n              <div \n                *ngFor=\"let exp of experience\"\n                class=\"experience-item\">\n                \n                <div class=\"experience-header\">\n                  <div class=\"experience-info\">\n                    <h4 class=\"experience-position\">{{ exp.position }}</h4>\n                    <div class=\"experience-company\">{{ exp.company }}</div>\n                    <div class=\"experience-period\">{{ exp.period }}</div>\n                  </div>\n                </div>\n                \n                <p class=\"experience-description\">{{ exp.description }}</p>\n                \n                <div class=\"experience-technologies\">\n                  <span \n                    *ngFor=\"let tech of exp.technologies\"\n                    class=\"tech-badge\">\n                    {{ tech }}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  `,\n  styleUrls: ['./education.component.scss']\n})\nexport class EducationComponent {\n  \n  education: Education[] = [\n    {\n      id: 1,\n      degree: 'Master en Informatique',\n      institution: 'Université de Nouakchott',\n      period: '2020 - 2022',\n      description: 'Spécialisation en développement web et cybersécurité avec focus sur les technologies modernes.',\n      icon: 'fas fa-university'\n    },\n    {\n      id: 2,\n      degree: 'Licence en Informatique',\n      institution: 'Université de Nouakchott',\n      period: '2017 - 2020',\n      description: 'Formation générale en informatique couvrant programmation, bases de données et réseaux.',\n      icon: 'fas fa-graduation-cap'\n    },\n    {\n      id: 3,\n      degree: 'Baccalauréat Scientifique',\n      institution: 'Lycée de Nouakchott',\n      period: '2016 - 2017',\n      description: 'Baccalauréat série Sciences Mathématiques avec mention Bien.',\n      icon: 'fas fa-school'\n    }\n  ];\n\n  experience: Experience[] = [\n    {\n      id: 1,\n      position: 'Développeur Full-stack',\n      company: 'TechSolutions Mauritanie',\n      period: '2022 - Présent',\n      description: 'Développement d\\'applications web complètes, gestion de projets et encadrement d\\'équipes junior.',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Docker', 'AWS']\n    },\n    {\n      id: 2,\n      position: 'Développeur Frontend',\n      company: 'Digital Agency MR',\n      period: '2021 - 2022',\n      description: 'Création d\\'interfaces utilisateur modernes et responsives pour diverses applications web.',\n      technologies: ['Vue.js', 'JavaScript', 'CSS3', 'Figma', 'Git']\n    },\n    {\n      id: 3,\n      position: 'Stagiaire Développeur',\n      company: 'StartupTech',\n      period: '2020 - 2021',\n      description: 'Apprentissage des bonnes pratiques de développement et contribution aux projets internes.',\n      technologies: ['HTML5', 'CSS3', 'JavaScript', 'PHP', 'MySQL']\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;IAgD5BC,EAPJ,CAAAC,cAAA,cAI8B,cAEE,cACD;IACzBD,EAAA,CAAAE,SAAA,QAA0B;IAC5BF,EAAA,CAAAG,YAAA,EAAM;IAIFH,EAFJ,CAAAC,cAAA,cAA2B,cACA,aACI;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAChDJ,EADgD,CAAAG,YAAA,EAAO,EACjD;IAENH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAG3DJ,EAH2D,CAAAG,YAAA,EAAI,EACrD,EACF,EACF;;;;;IAjBJH,EADA,CAAAK,WAAA,SAAAC,IAAA,WAA0B,UAAAA,IAAA,WACC;IAIpBN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,CAAAC,MAAA,CAAAC,IAAA,CAAkB;IAKQV,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAG,MAAA,CAAgB;IACbZ,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAI,MAAA,CAAgB;IAGdb,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAK,WAAA,CAAqB;IACvBd,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAM,WAAA,CAAqB;;;;;IA8BvDf,EAAA,CAAAC,cAAA,eAEqB;IACnBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAgB,kBAAA,MAAAC,OAAA,MACF;;;;;IAbEjB,EANN,CAAAC,cAAA,cAE0B,cAEO,cACA,aACK;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,cAAgC;IAAAD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAEnDJ,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;IAENH,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE3DH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAkB,UAAA,KAAAC,0CAAA,mBAEqB;IAIzBnB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfgCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,iBAAA,CAAAS,MAAA,CAAAC,QAAA,CAAkB;IAClBrB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAW,iBAAA,CAAAS,MAAA,CAAAE,OAAA,CAAiB;IAClBtB,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAS,MAAA,CAAAP,MAAA,CAAgB;IAIjBb,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAS,MAAA,CAAAL,WAAA,CAAqB;IAIlCf,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAuB,UAAA,YAAAH,MAAA,CAAAI,YAAA,CAAmB;;;AAcxD,OAAM,MAAOC,kBAAkB;EApF/BC,YAAA;IAsFE,KAAAC,SAAS,GAAgB,CACvB;MACEC,EAAE,EAAE,CAAC;MACLhB,MAAM,EAAE,wBAAwB;MAChCE,WAAW,EAAE,0BAA0B;MACvCD,MAAM,EAAE,aAAa;MACrBE,WAAW,EAAE,gGAAgG;MAC7GL,IAAI,EAAE;KACP,EACD;MACEkB,EAAE,EAAE,CAAC;MACLhB,MAAM,EAAE,yBAAyB;MACjCE,WAAW,EAAE,0BAA0B;MACvCD,MAAM,EAAE,aAAa;MACrBE,WAAW,EAAE,yFAAyF;MACtGL,IAAI,EAAE;KACP,EACD;MACEkB,EAAE,EAAE,CAAC;MACLhB,MAAM,EAAE,2BAA2B;MACnCE,WAAW,EAAE,qBAAqB;MAClCD,MAAM,EAAE,aAAa;MACrBE,WAAW,EAAE,8DAA8D;MAC3EL,IAAI,EAAE;KACP,CACF;IAED,KAAAmB,UAAU,GAAiB,CACzB;MACED,EAAE,EAAE,CAAC;MACLP,QAAQ,EAAE,wBAAwB;MAClCC,OAAO,EAAE,0BAA0B;MACnCT,MAAM,EAAE,gBAAgB;MACxBE,WAAW,EAAE,mGAAmG;MAChHS,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK;KAC9D,EACD;MACEI,EAAE,EAAE,CAAC;MACLP,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,mBAAmB;MAC5BT,MAAM,EAAE,aAAa;MACrBE,WAAW,EAAE,4FAA4F;MACzGS,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;KAC9D,EACD;MACEI,EAAE,EAAE,CAAC;MACLP,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EAAE,aAAa;MACtBT,MAAM,EAAE,aAAa;MACrBE,WAAW,EAAE,2FAA2F;MACxGS,YAAY,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO;KAC7D,CACF;;;;uBAtDUC,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhC,EAAA,CAAAiC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5ErBvC,EAHN,CAAAC,cAAA,iBAAkD,aACzB,aACyB,YAClB;UAAAD,EAAA,CAAAI,MAAA,kCAAsB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAI,MAAA,oDAAwC;UACtEJ,EADsE,CAAAG,YAAA,EAAI,EACpE;UAKFH,EAHJ,CAAAC,cAAA,aAA+B,aAEkB,YACrB;UACtBD,EAAA,CAAAE,SAAA,YAAqC;UACrCF,EAAA,CAAAI,MAAA,mBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,cAAsB;UACpBD,EAAA,CAAAkB,UAAA,KAAAuB,kCAAA,oBAI8B;UAmBlCzC,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAC,cAAA,eAAgD,aACtB;UACtBD,EAAA,CAAAE,SAAA,aAAgC;UAChCF,EAAA,CAAAI,MAAA,yCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAkB,UAAA,KAAAwB,kCAAA,mBAE0B;UAwBpC1C,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACE;;;UA1DkBH,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAuB,UAAA,YAAAiB,GAAA,CAAAb,SAAA,CAAc;UAiCd3B,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAuB,UAAA,YAAAiB,GAAA,CAAAX,UAAA,CAAa;;;qBApDjC9B,YAAY,EAAA4C,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}