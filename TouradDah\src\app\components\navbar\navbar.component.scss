.navbar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--navbar-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.navbar {
  position: relative;
}

.scroll-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--gradient-primary);
  transition: width 0.3s ease;
  z-index: 1001;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.menu-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;

  li a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: var(--primary-color);
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 0;
      height: 2px;
      background: var(--primary-color);
      transition: width 0.3s ease;
    }

    &:hover::after {
      width: 100%;
    }
  }
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
  }

  i {
    font-size: 16px;
  }
}

.menu-icon {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color);
    color: white;
  }

  i {
    font-size: 18px;
  }
}

// Responsive
@media (max-width: 768px) {
  .menu-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background: var(--navbar-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    padding: 1rem;
    gap: 1rem;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.show {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }

    li a {
      display: block;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--border-color);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .menu-icon {
    display: flex;
  }

  nav {
    padding: 0.8rem 1rem;
  }

  .logo {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  nav {
    padding: 0.6rem 1rem;
  }

  .logo {
    font-size: 18px;
  }

  .theme-toggle,
  .menu-icon {
    width: 35px;
    height: 35px;
  }

  .theme-toggle i,
  .menu-icon i {
    font-size: 14px;
  }
}
