{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class AboutComponent {\n  static {\n    this.ɵfac = function AboutComponent_Factory(t) {\n      return new (t || AboutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AboutComponent,\n      selectors: [[\"app-about\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 58,\n      vars: 0,\n      consts: [[\"id\", \"about\", 1, \"about-section\"], [1, \"container\"], [1, \"section-header\", \"animate-on-scroll\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"about-content\"], [1, \"about-text\", \"animate-on-scroll\"], [1, \"intro-text\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"specializations\"], [1, \"spec-list\"], [1, \"spec-item\"], [1, \"fas\", \"fa-code\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"fas\", \"fa-bug\"], [1, \"fas\", \"fa-mobile-alt\"], [1, \"about-image\", \"animate-on-scroll\"], [1, \"image-wrapper\"], [\"src\", \"assets/images/Tourad2.jpg\", \"alt\", \"Tourad Dah au travail\"], [1, \"image-overlay\"], [1, \"overlay-content\"], [1, \"fas\", \"fa-laptop-code\"]],\n      template: function AboutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"\\u00C0 Propos de Moi\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"D\\u00E9couvrez mon parcours et ma passion pour le d\\u00E9veloppement\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"p\");\n          i0.ɵɵtext(11, \" Passionn\\u00E9 par le d\\u00E9veloppement web et la cybers\\u00E9curit\\u00E9, je suis un d\\u00E9veloppeur full-stack avec une expertise particuli\\u00E8re dans la cr\\u00E9ation d'applications web modernes et s\\u00E9curis\\u00E9es. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \" Mon parcours m'a permis d'acqu\\u00E9rir une solide exp\\u00E9rience dans diverses technologies, allant du d\\u00E9veloppement frontend avec des frameworks modernes au d\\u00E9veloppement backend avec des architectures robustes. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"div\", 10);\n          i0.ɵɵtext(17, \"3+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 11);\n          i0.ɵɵtext(19, \"Ann\\u00E9es d'exp\\u00E9rience\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 9)(21, \"div\", 10);\n          i0.ɵɵtext(22, \"50+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 11);\n          i0.ɵɵtext(24, \"Projets r\\u00E9alis\\u00E9s\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"div\", 10);\n          i0.ɵɵtext(27, \"15+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 11);\n          i0.ɵɵtext(29, \"Technologies ma\\u00EEtris\\u00E9es\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 12)(31, \"h3\");\n          i0.ɵɵtext(32, \"Mes sp\\u00E9cialisations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 13)(34, \"div\", 14);\n          i0.ɵɵelement(35, \"i\", 15);\n          i0.ɵɵelementStart(36, \"span\");\n          i0.ɵɵtext(37, \"D\\u00E9veloppement Full-stack\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 14);\n          i0.ɵɵelement(39, \"i\", 16);\n          i0.ɵɵelementStart(40, \"span\");\n          i0.ɵɵtext(41, \"Cybers\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 14);\n          i0.ɵɵelement(43, \"i\", 17);\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \"Bug Bounty\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 14);\n          i0.ɵɵelement(47, \"i\", 18);\n          i0.ɵɵelementStart(48, \"span\");\n          i0.ɵɵtext(49, \"Applications Mobiles\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(50, \"div\", 19)(51, \"div\", 20);\n          i0.ɵɵelement(52, \"img\", 21);\n          i0.ɵɵelementStart(53, \"div\", 22)(54, \"div\", 23);\n          i0.ɵɵelement(55, \"i\", 24);\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"Toujours en apprentissage\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n      },\n      dependencies: [CommonModule],\n      styles: [\".about-section[_ngcontent-%COMP%] {\\n  padding: 100px 0;\\n  background: var(--bg-color);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 4rem;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n  position: relative;\\n}\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: var(--gradient-primary);\\n  border-radius: 2px;\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: var(--gray);\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.about-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 4rem;\\n  align-items: center;\\n}\\n\\n.about-text[_ngcontent-%COMP%]   .intro-text[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.about-text[_ngcontent-%COMP%]   .intro-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.8;\\n  color: var(--text-color);\\n  margin-bottom: 1.5rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 1.5rem;\\n  margin: 2rem 0;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1.5rem;\\n  background: var(--card-bg);\\n  border-radius: 15px;\\n  border: 1px solid var(--border-color);\\n  transition: all 0.3s ease;\\n}\\n.stat-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n.stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  margin-bottom: 0.5rem;\\n}\\n.stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--gray);\\n  font-weight: 500;\\n}\\n\\n.specializations[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n}\\n\\n.spec-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 1rem;\\n}\\n\\n.spec-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.8rem;\\n  padding: 1rem;\\n  background: var(--card-bg);\\n  border-radius: 10px;\\n  border: 1px solid var(--border-color);\\n  transition: all 0.3s ease;\\n}\\n.spec-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  transform: translateX(5px);\\n}\\n.spec-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.2rem;\\n}\\n.spec-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--text-color);\\n  font-weight: 500;\\n}\\n\\n.about-image[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 400px;\\n  width: 100%;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n.image-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  display: block;\\n  transition: transform 0.3s ease;\\n}\\n.image-wrapper[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.image-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(3, 59, 74, 0.8), rgba(32, 201, 151, 0.8));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.image-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: white;\\n}\\n.image-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 1rem;\\n}\\n.image-overlay[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.image-wrapper[_ngcontent-%COMP%]:hover   .image-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 768px) {\\n  .about-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 2rem;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .spec-list[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .about-section[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .stat-item[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .stat-number[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "AboutComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AboutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\about\\about.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-about',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <section id=\"about\" class=\"about-section\">\n      <div class=\"container\">\n        <div class=\"section-header animate-on-scroll\">\n          <h2 class=\"section-title\">À Propos de Moi</h2>\n          <p class=\"section-subtitle\">Découvrez mon parcours et ma passion pour le développement</p>\n        </div>\n\n        <div class=\"about-content\">\n          <div class=\"about-text animate-on-scroll\">\n            <div class=\"intro-text\">\n              <p>\n                Passionné par le développement web et la cybersécurité, je suis un développeur full-stack \n                avec une expertise particulière dans la création d'applications web modernes et sécurisées.\n              </p>\n              <p>\n                Mon parcours m'a permis d'acquérir une solide expérience dans diverses technologies, \n                allant du développement frontend avec des frameworks modernes au développement backend \n                avec des architectures robustes.\n              </p>\n            </div>\n\n            <div class=\"stats-grid\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">3+</div>\n                <div class=\"stat-label\">Années d'expérience</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">50+</div>\n                <div class=\"stat-label\">Projets réalisés</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">15+</div>\n                <div class=\"stat-label\">Technologies maîtrisées</div>\n              </div>\n            </div>\n\n            <div class=\"specializations\">\n              <h3>Mes spécialisations</h3>\n              <div class=\"spec-list\">\n                <div class=\"spec-item\">\n                  <i class=\"fas fa-code\"></i>\n                  <span>Développement Full-stack</span>\n                </div>\n                <div class=\"spec-item\">\n                  <i class=\"fas fa-shield-alt\"></i>\n                  <span>Cybersécurité</span>\n                </div>\n                <div class=\"spec-item\">\n                  <i class=\"fas fa-bug\"></i>\n                  <span>Bug Bounty</span>\n                </div>\n                <div class=\"spec-item\">\n                  <i class=\"fas fa-mobile-alt\"></i>\n                  <span>Applications Mobiles</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"about-image animate-on-scroll\">\n            <div class=\"image-wrapper\">\n              <img src=\"assets/images/Tourad2.jpg\" alt=\"Tourad Dah au travail\">\n              <div class=\"image-overlay\">\n                <div class=\"overlay-content\">\n                  <i class=\"fas fa-laptop-code\"></i>\n                  <p>Toujours en apprentissage</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  `,\n  styleUrls: ['./about.component.scss']\n})\nexport class AboutComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAmF9C,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzEjBP,EAHN,CAAAS,cAAA,iBAA0C,aACjB,aACyB,YAClB;UAAAT,EAAA,CAAAU,MAAA,2BAAe;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAC9CX,EAAA,CAAAS,cAAA,WAA4B;UAAAT,EAAA,CAAAU,MAAA,2EAA0D;UACxFV,EADwF,CAAAW,YAAA,EAAI,EACtF;UAKAX,EAHN,CAAAS,cAAA,aAA2B,aACiB,aAChB,SACnB;UACDT,EAAA,CAAAU,MAAA,4OAEF;UAAAV,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,SAAG;UACDT,EAAA,CAAAU,MAAA,0OAGF;UACFV,EADE,CAAAW,YAAA,EAAI,EACA;UAIFX,EAFJ,CAAAS,cAAA,cAAwB,cACC,eACI;UAAAT,EAAA,CAAAU,MAAA,UAAE;UAAAV,EAAA,CAAAW,YAAA,EAAM;UACjCX,EAAA,CAAAS,cAAA,eAAwB;UAAAT,EAAA,CAAAU,MAAA,qCAAmB;UAC7CV,EAD6C,CAAAW,YAAA,EAAM,EAC7C;UAEJX,EADF,CAAAS,cAAA,cAAuB,eACI;UAAAT,EAAA,CAAAU,MAAA,WAAG;UAAAV,EAAA,CAAAW,YAAA,EAAM;UAClCX,EAAA,CAAAS,cAAA,eAAwB;UAAAT,EAAA,CAAAU,MAAA,kCAAgB;UAC1CV,EAD0C,CAAAW,YAAA,EAAM,EAC1C;UAEJX,EADF,CAAAS,cAAA,cAAuB,eACI;UAAAT,EAAA,CAAAU,MAAA,WAAG;UAAAV,EAAA,CAAAW,YAAA,EAAM;UAClCX,EAAA,CAAAS,cAAA,eAAwB;UAAAT,EAAA,CAAAU,MAAA,yCAAuB;UAEnDV,EAFmD,CAAAW,YAAA,EAAM,EACjD,EACF;UAGJX,EADF,CAAAS,cAAA,eAA6B,UACvB;UAAAT,EAAA,CAAAU,MAAA,gCAAmB;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAE1BX,EADF,CAAAS,cAAA,eAAuB,eACE;UACrBT,EAAA,CAAAY,SAAA,aAA2B;UAC3BZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,qCAAwB;UAChCV,EADgC,CAAAW,YAAA,EAAO,EACjC;UACNX,EAAA,CAAAS,cAAA,eAAuB;UACrBT,EAAA,CAAAY,SAAA,aAAiC;UACjCZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,+BAAa;UACrBV,EADqB,CAAAW,YAAA,EAAO,EACtB;UACNX,EAAA,CAAAS,cAAA,eAAuB;UACrBT,EAAA,CAAAY,SAAA,aAA0B;UAC1BZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,kBAAU;UAClBV,EADkB,CAAAW,YAAA,EAAO,EACnB;UACNX,EAAA,CAAAS,cAAA,eAAuB;UACrBT,EAAA,CAAAY,SAAA,aAAiC;UACjCZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,4BAAoB;UAIlCV,EAJkC,CAAAW,YAAA,EAAO,EAC7B,EACF,EACF,EACF;UAGJX,EADF,CAAAS,cAAA,eAA2C,eACd;UACzBT,EAAA,CAAAY,SAAA,eAAiE;UAE/DZ,EADF,CAAAS,cAAA,eAA2B,eACI;UAC3BT,EAAA,CAAAY,SAAA,aAAkC;UAClCZ,EAAA,CAAAS,cAAA,SAAG;UAAAT,EAAA,CAAAU,MAAA,iCAAyB;UAO1CV,EAP0C,CAAAW,YAAA,EAAI,EAC5B,EACF,EACF,EACF,EACF,EACF,EACE;;;qBA1EFhB,YAAY;MAAAkB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}