<section id="skills" class="skills-section">
  <div class="container">
    <div class="section-header animate-on-scroll">
      <h2 class="section-title">Mes Compétences</h2>
      <p class="section-subtitle">Technologies et outils que je maîtrise</p>
    </div>

    <!-- Filtres de catégories -->
    <div class="category-filters animate-on-scroll">
      <button 
        *ngFor="let category of categories"
        class="filter-btn"
        [class.active]="category.active"
        (click)="filterSkills(category.id)">
        {{ category.name }}
      </button>
    </div>

    <!-- Grille des compétences -->
    <div class="skills-grid animate-on-scroll">
      <div 
        *ngFor="let skill of filteredSkills; trackBy: trackBySkill"
        class="skill-card"
        [attr.data-category]="skill.category">
        
        <div class="skill-icon">
          <img [src]="skill.icon" [alt]="skill.name + ' logo'">
        </div>
        
        <div class="skill-info">
          <h3 class="skill-name">{{ skill.name }}</h3>
          <div class="skill-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                [style.width.%]="skill.level"
                [attr.data-level]="skill.level">
              </div>
            </div>
            <span class="skill-percentage">{{ skill.level }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Section des compétences par catégorie (alternative) -->
    <div class="skills-by-category" style="display: none;">
      <div class="category-section" *ngFor="let category of categories.slice(1)">
        <h3 class="category-title">{{ category.name }}</h3>
        <div class="category-skills">
          <div 
            *ngFor="let skill of getSkillsByCategory(category.id)"
            class="skill-item">
            <div class="skill-header">
              <img [src]="skill.icon" [alt]="skill.name">
              <span class="skill-name">{{ skill.name }}</span>
              <span class="skill-level">{{ skill.level }}%</span>
            </div>
            <div class="skill-bar">
              <div 
                class="skill-fill" 
                [style.width.%]="skill.level">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
