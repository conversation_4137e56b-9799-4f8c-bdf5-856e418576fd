{"name": "angular-portfolio", "version": "0.1.0", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "dependencies": {"@angular/animations": "~14.0.0", "@angular/common": "~14.0.0", "@angular/compiler": "~14.0.0", "@angular/core": "~14.0.0", "@angular/forms": "~14.0.0", "@angular/platform-browser": "~14.0.0", "@angular/platform-browser-dynamic": "~14.0.0", "@angular/router": "~14.0.0", "rxjs": "~7.0.0", "tslib": "^2.0.0", "zone.js": "~0.11.0"}, "devDependencies": {"@angular-devkit/build-angular": "~14.0.0", "@angular/cli": "~14.0.0", "@angular/compiler-cli": "~14.0.0", "@types/node": "^12.11.7", "typescript": "~4.6.0"}, "engines": {"node": ">=12.0.0"}}