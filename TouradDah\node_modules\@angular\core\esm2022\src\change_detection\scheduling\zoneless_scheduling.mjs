/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Injectable that is notified when an `LView` is made aware of changes to application state.
 */
export class ChangeDetectionScheduler {
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiem9uZWxlc3Nfc2NoZWR1bGluZy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL2NoYW5nZV9kZXRlY3Rpb24vc2NoZWR1bGluZy96b25lbGVzc19zY2hlZHVsaW5nLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQU9IOztHQUVHO0FBQ0gsTUFBTSxPQUFnQix3QkFBd0I7Q0FFN0MiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuZXhwb3J0IGNvbnN0IGVudW0gTm90aWZpY2F0aW9uVHlwZSB7XG4gIFJlZnJlc2hWaWV3cyxcbiAgQWZ0ZXJSZW5kZXJIb29rcyxcbn1cblxuLyoqXG4gKiBJbmplY3RhYmxlIHRoYXQgaXMgbm90aWZpZWQgd2hlbiBhbiBgTFZpZXdgIGlzIG1hZGUgYXdhcmUgb2YgY2hhbmdlcyB0byBhcHBsaWNhdGlvbiBzdGF0ZS5cbiAqL1xuZXhwb3J0IGFic3RyYWN0IGNsYXNzIENoYW5nZURldGVjdGlvblNjaGVkdWxlciB7XG4gIGFic3RyYWN0IG5vdGlmeShzb3VyY2U/OiBOb3RpZmljYXRpb25UeXBlKTogdm9pZDtcbn1cbiJdfQ==