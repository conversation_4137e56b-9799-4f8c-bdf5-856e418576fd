/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createContentQuery, createViewQuery } from '../query';
import { bindQueryToSignal } from '../query_reactive';
import { getCurrentQueryIndex, setCurrentQueryIndex } from '../state';
/**
 * Creates a new content query and binds it to a signal created by an authoring function.
 *
 * @param directiveIndex Current directive index
 * @param target The target signal to which the query should be bound
 * @param predicate The type for which the query will search
 * @param flags Flags associated with the query
 * @param read What to save in the query
 *
 * @codeGenApi
 */
export function ɵɵcontentQuerySignal(directiveIndex, target, predicate, flags, read) {
    bindQueryToSignal(target, createContentQuery(directiveIndex, predicate, flags, read));
}
/**
 * Creates a new view query by initializing internal data structures and binding a new query to the
 * target signal.
 *
 * @param target The target signal to assign the query results to.
 * @param predicate The type or label that should match a given query
 * @param flags Flags associated with the query
 * @param read What to save in the query
 *
 * @codeGenApi
 */
export function ɵɵviewQuerySignal(target, predicate, flags, read) {
    bindQueryToSignal(target, createViewQuery(predicate, flags, read));
}
/**
 * Advances the current query index by a specified offset.
 *
 * Adjusting the current query index is necessary in cases where a given directive has a mix of
 * zone-based and signal-based queries. The signal-based queries don't require tracking of the
 * current index (those are refreshed on demand and not during change detection) so this instruction
 * is only necessary for backward-compatibility.
 *
 * @param index offset to apply to the current query index (defaults to 1)
 *
 * @codeGenApi
 */
export function ɵɵqueryAdvance(indexOffset = 1) {
    setCurrentQueryIndex(getCurrentQueryIndex() + indexOffset);
}
//# sourceMappingURL=data:application/json;base64,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