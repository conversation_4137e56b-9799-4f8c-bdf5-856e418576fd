.skills-section {
  padding: 100px 0;
  background: var(--bg-primary);
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
  }
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--gray);
  max-width: 600px;
  margin: 0 auto;
}

.category-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-color);
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
  }

  &.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 5px 15px rgba(32, 201, 151, 0.3);
  }
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.skill-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(32, 201, 151, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);

    &::before {
      left: 100%;
    }
  }
}

.skill-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color);
  border-radius: 50%;
  border: 3px solid var(--border-color);
  transition: all 0.3s ease;

  img {
    width: 50px;
    height: 50px;
    object-fit: contain;
  }
}

.skill-card:hover .skill-icon {
  border-color: var(--primary-color);
  transform: scale(1.1);
}

.skill-info {
  position: relative;
  z-index: 1;
}

.skill-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.skill-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 4px;
  transition: width 1s ease-in-out;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
  }
}

.skill-percentage {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.9rem;
  min-width: 40px;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// Styles pour la vue alternative par catégorie
.skills-by-category {
  .category-section {
    margin-bottom: 3rem;
  }

  .category-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    display: inline-block;
  }

  .category-skills {
    display: grid;
    gap: 1rem;
  }

  .skill-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 1.5rem;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--primary-color);
      transform: translateX(5px);
    }
  }

  .skill-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;

    img {
      width: 30px;
      height: 30px;
      object-fit: contain;
    }

    .skill-name {
      flex: 1;
      font-weight: 600;
      color: var(--text-color);
    }

    .skill-level {
      font-weight: 600;
      color: var(--primary-color);
    }
  }

  .skill-bar {
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
  }

  .skill-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    transition: width 1s ease-in-out;
  }
}

// Responsive
@media (max-width: 768px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .skill-card {
    padding: 1.5rem;
  }

  .skill-icon {
    width: 70px;
    height: 70px;

    img {
      width: 40px;
      height: 40px;
    }
  }

  .section-title {
    font-size: 2rem;
  }

  .category-filters {
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .skills-section {
    padding: 60px 0;
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .skill-card {
    padding: 1rem;
  }

  .skill-icon {
    width: 60px;
    height: 60px;

    img {
      width: 35px;
      height: 35px;
    }
  }

  .skill-name {
    font-size: 1.1rem;
  }

  .category-filters {
    flex-direction: column;
    align-items: center;
  }

  .filter-btn {
    width: 200px;
    text-align: center;
  }
}

@media (max-width: 375px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.8rem;
  }

  .skill-card {
    padding: 0.8rem;
  }

  .skill-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 1rem;

    img {
      width: 30px;
      height: 30px;
    }
  }

  .skill-name {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .skill-progress {
    flex-direction: column;
    gap: 0.5rem;
  }

  .progress-bar {
    width: 100%;
  }
}
