import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule],
  template: `
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3 class="footer-title">Tourad Dah</h3>
            <p class="footer-description">
              Développeur Full-stack passionné par la création d'applications web modernes et sécurisées.
            </p>
            <div class="social-links">
              <a href="https://github.com/tourad" target="_blank" rel="noopener" title="GitHub">
                <i class="fa-brands fa-github"></i>
              </a>
              <a href="#" title="Facebook">
                <i class="fa-brands fa-square-facebook"></i>
              </a>
              <a href="#" target="_blank" rel="noopener" title="Twitter">
                <i class="fa-brands fa-square-x-twitter"></i>
              </a>
              <a href="#" title="LinkedIn">
                <i class="fa-brands fa-linkedin"></i>
              </a>
              <a href="http://wa.me/+22238493149" target="_blank" rel="noopener" title="WhatsApp">
                <i class="fa-brands fa-whatsapp"></i>
              </a>
            </div>
          </div>

          <div class="footer-section">
            <h4 class="section-title">Navigation</h4>
            <ul class="footer-links">
              <li><a href="#home" (click)="scrollToSection('home')">Accueil</a></li>
              <li><a href="#about" (click)="scrollToSection('about')">À Propos</a></li>
              <li><a href="#skills" (click)="scrollToSection('skills')">Compétences</a></li>
              <li><a href="#portfolio" (click)="scrollToSection('portfolio')">Projets</a></li>
              <li><a href="#contact" (click)="scrollToSection('contact')">Contact</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="section-title">Contact</h4>
            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>+222 38 49 31 49</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>Nouakchott, Mauritanie</span>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <div class="copyright">
            <p>&copy; {{ currentYear }} Tourad Med Mahmoud Dah. Tous droits réservés.</p>
          </div>
          <div class="footer-links-bottom">
            <a href="#" class="footer-link">Politique de confidentialité</a>
            <a href="#" class="footer-link">Conditions d'utilisation</a>
          </div>
        </div>
      </div>
    </footer>
  `,
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent {
  currentYear = new Date().getFullYear();

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  }
}
