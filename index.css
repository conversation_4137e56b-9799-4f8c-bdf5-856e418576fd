:root{
    --primary-color: #033b4a;
    --secondar-color:#20c997;
    --gray: #4a4a4d;
    --bg-primary: #ecf0f3;
    --shadow-light: rgba(255, 255, 255, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.2);
    --gradient-primary: linear-gradient(135deg, #033b4a 0%, #20c997 100%);
    --gradient-secondary: linear-gradient(45deg, #20c997 0%, #15a076 100%);

    /* Variables pour le mode clair */
    --bg-color: #ffffff;
    --text-color: #333333;
    --card-bg: #ffffff;
    --navbar-bg: #ffffff;
    --border-color: #e0e0e0;
}

/* Mode sombre */
[data-theme="dark"] {
    --primary-color: #20c997;
    --bg-primary: #1a1a1a;
    --bg-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e1e;
    --navbar-bg: #1e1e1e;
    --border-color: #333333;
    --gray: #cccccc;
}
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}
html {
    scroll-behavior: smooth;
}

/* Animations et Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}



/* Classes d'animation */
.animate-on-scroll {
    opacity: 0;
    transition: all 0.8s ease;
}

.animate-on-scroll.animate {
    opacity: 1;
}

/* Effet machine à écrire */
.typewriter {
    border-right: 3px solid var(--secondar-color);
    white-space: nowrap;
}
body{
    font-family:'Times New Roman', Times, serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}
h1,h2{
    font-family:'Times New Roman', Times, serif;
}
ul{
    list-style: none;

}
a{
    text-decoration: none;

}

.text-pimary{
    color: var(--secondar-color);
}
.btn-primary{
    background-color: var(--secondar-color);
    color: #fff;
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}
.btn-primary:hover{
    background-color: #15a076;
}
.container{
    max-width: 1100px;
    margin: auto;
    padding: 0 2rem;
}


header{
    background-color: var(--bg-primary);
    height: 100vh;

}
header .navbar{
    background-color: var(--navbar-bg);
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    position: fixed;
    z-index: 1000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Barre de progression de lecture */
.scroll-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    background: var(--gradient-secondary);
    width: 0%;
    transition: width 0.1s ease;
    z-index: 1001;
    border-radius: 0 2px 2px 0;
    box-shadow: 0 0 10px rgba(32, 201, 151, 0.5);
    opacity: 0;
    transform: scaleX(0);
    transform-origin: left;
    animation: progressFadeIn 0.3s ease forwards;
}

.scroll-progress.visible {
    opacity: 1;
    transform: scaleX(1);
}

.scroll-progress::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(32, 201, 151, 0.8));
    border-radius: 0 2px 2px 0;
}

@keyframes progressFadeIn {
    from {
        opacity: 0;
        transform: scaleX(0);
    }
    to {
        opacity: 1;
        transform: scaleX(1);
    }
}
header .navbar nav{
    display: flex;
    justify-content: space-between;
    max-width: 1100px;
    margin: auto;
    padding: 1rem 2rem;
    min-height: 60px;
    align-items: center;
}

#home #logo{
    color: var(--primary-color);
    font-size: 24px;
    font-weight: 500;

}
header .navbar nav ul{
    display: flex;
    justify-content: center;
    align-items: center;
}
header .navbar nav ul a{
    margin: 0 10px;
    color: var(--primary-color);
    font-weight: 600;
    
}
header .header-content .content-text{
    margin-bottom: 5rem;
}

/* Sections de la page d'accueil */
.greeting-section {
    margin-bottom: 2rem;
}

.subtitle {
    font-size: 18px;
    color: var(--gray);
    margin-top: 1rem;
    font-weight: 400;
    line-height: 1.5;
}

.skills-highlight {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.skill-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(32, 201, 151, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 25px;
    border: 1px solid rgba(32, 201, 151, 0.2);
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.skill-badge:hover {
    background: rgba(32, 201, 151, 0.2);
    transform: translateY(-2px);
}

.skill-badge i {
    color: var(--secondar-color);
    font-size: 16px;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* Bouton principal */
.btn-primary.enhanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 16px 32px;
    background: var(--gradient-secondary);
    color: white;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(32, 201, 151, 0.3);
}

.btn-primary.enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(32, 201, 151, 0.4);
}




header .header-content{
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    align-items: center;
    max-width: 1200px;
    height: 100%;
    padding: 5rem 2rem 0;
    margin: auto;
    gap: 3rem;
    min-height: 80vh;
}
#home .header-content p span{
    font-weight: 700;
    font-size: 20px;
}
#home .header-content h1{
    font-size: 60px;
    color:var(--primary-color);
    text-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
    margin-top: 10px;
}
#home .header-content .title{
    font-size: 20px;
    color:var(--primary-color);
    margin: 10px 0;
    font-weight: 500;
}
#home .header-content .social{
    margin: 1rem 0 2rem;
}
#home .header-content .social a i{
    font-size: 18px;
    padding: 10px;
    background: rgb(38, 20, 138);
    color:#ffff;
    border-radius: 50%;
    margin-right: 10px;
}
#home .header-content .social a i:hover{
    transform: scale(0.85);
    transition: all 0.35 ease;
}
/* Image Home Section */
.home__img {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.image-container {
    position: relative;
    max-width: 400px;
    width: 100%;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.image-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.image-container img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 20px;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(32, 201, 151, 0.1), rgba(32, 201, 151, 0.05));
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 20px;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

.floating-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--gradient-secondary);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 3s ease-in-out infinite;
}

.floating-badge i {
    font-size: 16px;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

header .header-content img{
    max-width: 400px;
    border-radius: 0 20px;
}
/*about*/
#about , #skills , #portfolio{
    padding: 6rem 0;
}
#about h2 , #skills h2 , #contact h2,#portfolio h2{
    text-align: center;
    font-size: 38px;
    color: var(--primary-color);
    font-weight: 600;
    letter-spacing: 1px;
    margin-top: 1rem;
}
#about hr, #skills hr , #portfolio hr{
    width: 100px;
    height: 3px;
    background-color: var(--secondar-color);
    border: none;
    margin: 5px auto 0;
}
#about .about-content{
    margin-top: 4rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    column-gap: 3rem;
}
#about .about-content .colon1 h3{
    font-size: 28;
    color: var(--primary-color);
    margin-bottom: 20px;
}
#about .about-content .colon1 p{
    color: gray;
    margin-bottom: 20px;
    line-height: 1.8;
}
#about .about-content .colon2 p{
    color: gray;
    margin-bottom: 20px;
    line-height: 1.8;
    padding-bottom: 5px;
    border-bottom: 1px solid;
}

/* Section Spécialités */
.specialties-section {
    margin-top: 4rem;
    padding: 2rem 0;
}

.specialties-title {
    text-align: center;
    font-size: 32px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 3rem;
}

.specialties-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

.specialties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.specialty-card {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.specialty-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(32, 201, 151, 0.2);
}

.specialty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.specialty-icon i {
    font-size: 32px;
    color: white;
}

.specialty-card:hover .specialty-icon {
    transform: scale(1.1);
}

.specialty-card h4 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.specialty-card p {
    color: var(--gray);
    line-height: 1.6;
    font-size: 16px;
    transition: color 0.3s ease;
}

.specialty-card:hover p {
    color: var(--primary-color);
}

/*about*/

/*skil*/
#skills .row img {
    max-width: 40px;
}
#skills,#Education{
    background-color: var(--primary-color);
}
#skills h2,#Education h2,#portfolio h2{
    color:#ffff;
}
#skills .skills-content{
    margin-top: 2rem;
    background-color: #0f272d;
    border-radius: 15px;
    padding: 2rem;
}

/* Catégories de compétences */
.skill-category {
    margin-bottom: 3rem;
}

.skill-category:last-child {
    margin-bottom: 0;
}

.category-title {
    color: var(--secondar-color);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    padding-bottom: 10px;
}

.category-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

.category-title i {
    margin-right: 10px;
    font-size: 20px;
}

#skills .skills-content .row{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    align-items: center;
    text-align: center;
    margin-bottom: 1rem;
}
#skills .skills-content .row .item{
    color: #ffff;
    background: linear-gradient(145deg, #1a1a1a, #000);
    margin: 20px;
    padding: 1.5rem;
    border-radius: 15px;
    font-size: 18px;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    border: 1px solid transparent;
}

#skills .skills-content .row .item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(32, 201, 151, 0.2), transparent);
    transition: left 0.5s;
}

#skills .skills-content .row .item:hover::before {
    left: 100%;
}

#skills .skills-content .row .item:hover{
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(32, 201, 151, 0.3);
    border: 1px solid var(--secondar-color);
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
}

#skills .skills-content .row .item img {
    transition: all 0.4s ease;
    filter: brightness(0.8);
}

#skills .skills-content .row .item:hover img {
    transform: rotateY(360deg) scale(1.1);
    filter: brightness(1.2) drop-shadow(0 0 10px rgba(32, 201, 151, 0.5));
}

#skills .skills-content .row .item p {
    margin-top: 10px;
    transition: all 0.3s ease;
}

#skills .skills-content .row .item:hover p {
    color: var(--secondar-color);
    text-shadow: 0 0 10px rgba(32, 201, 151, 0.5);
}

/* Icônes personnalisées pour les nouvelles compétences */
.skill-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin: 0 auto 10px;
    transition: all 0.4s ease;
    background: transparent;
}

.nextjs-icon svg,
.laravel-icon svg {
    transition: all 0.4s ease;
    filter: brightness(0.9);
}

.nextjs-icon {
    background: transparent;
}

.laravel-icon {
    background: transparent;
}

/* Barres de progression des compétences */
.skill-progress {
    width: 100%;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: 3px;
    width: 0%;
    transition: width 1.5s ease-in-out;
}

.percentage {
    color: var(--secondar-color);
    font-size: 12px;
    font-weight: 600;
    margin-top: 5px;
    display: block;
    text-align: center;
}

/* Styles pour les stacks */
.stack-row {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.stack-item {
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    border: 1px solid var(--secondar-color);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.4s ease;
}

.stack-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(32, 201, 151, 0.3);
    border-color: #fff;
}

.stack-header h4 {
    color: var(--secondar-color);
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
}

.stack-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 15px;
}

.tech-badge {
    background: rgba(32, 201, 151, 0.2);
    color: var(--secondar-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid rgba(32, 201, 151, 0.3);
    transition: all 0.3s ease;
}

.tech-badge:hover {
    background: var(--secondar-color);
    color: #000;
    transform: scale(1.05);
}


/*skil*/
#contact{
    background-color: #00f4ab0c;
}
/* Education Section */
#Education {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #044a5a 100%);
}

#Education h2 {
    text-align: center;
    font-size: 38px;
    color: #ffffff;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

#Education hr {
    width: 100px;
    height: 3px;
    background-color: var(--secondar-color);
    border: none;
    margin: 5px auto 3rem;
}

/* Timeline Education */
.education-timeline {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
}

.timeline-container {
    position: relative;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, var(--secondar-color), rgba(32, 201, 151, 0.3));
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 4rem;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.timeline-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: 0;
    margin-right: 50%;
    padding-right: 3rem;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 50%;
    margin-right: 0;
    padding-left: 3rem;
}

.timeline-dot {
    position: absolute;
    left: 50%;
    top: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateX(-50%);
    z-index: 2;
    transition: all 0.4s ease;
}

.timeline-dot.current {
    background: linear-gradient(135deg, var(--secondar-color), #15a076);
    box-shadow: 0 0 0 8px rgba(32, 201, 151, 0.2);
    animation: pulse 2s infinite;
}

.timeline-dot.completed {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
}

.timeline-dot i {
    font-size: 24px;
    color: white;
}



/* Education Cards */
.education-card {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid transparent;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.education-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(32, 201, 151, 0.1), transparent);
    transition: left 0.6s;
}

.education-card:hover::before {
    left: 100%;
}

.education-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(32, 201, 151, 0.2);
    border-color: var(--secondar-color);
}

.education-card.current {
    border: 2px solid var(--secondar-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.institution-logo {
    width: 80px;
    height: 80px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.institution-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(32, 201, 151, 0.2);
}

.college-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-radius: 15px;
}

.education-card:hover .college-image {
    transform: scale(1.1);
}

.card-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.current-badge {
    background: linear-gradient(135deg, var(--secondar-color), #15a076);
    color: white;
}

.completed-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.degree {
    font-size: 24px;
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.specialization {
    font-size: 18px;
    color: var(--secondar-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.institution {
    font-size: 16px;
    color: var(--gray);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.education-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray);
    font-size: 14px;
}

.detail-item i {
    color: var(--secondar-color);
    font-size: 16px;
}

.skills-acquired h5 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 1rem;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag {
    background: rgba(32, 201, 151, 0.1);
    color: var(--secondar-color);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(32, 201, 151, 0.2);
    transition: all 0.3s ease;
}

.skill-tag:hover {
    background: var(--secondar-color);
    color: white;
    transform: scale(1.05);
}
/* Portfolio start */
#portfolio {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

#portfolio h2 {
    text-align: center;
    font-size: 38px;
    color: var(--primary-color);
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

#portfolio hr {
    width: 100px;
    height: 3px;
    background-color: var(--secondar-color);
    border: none;
    margin: 5px auto 2rem;
}

.portfolio-description {
    text-align: center;
    font-size: 18px;
    color: var(--gray);
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.6;
}

/* Filtres Portfolio */
.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 12px 24px;
    border: 2px solid var(--secondar-color);
    background: transparent;
    color: var(--secondar-color);
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--secondar-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(32, 201, 151, 0.3);
}

/* Grid Portfolio */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.portfolio-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.portfolio-item.show {
    opacity: 1;
    transform: translateY(0);
}

.portfolio-card {
    background: var(--card-bg);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.portfolio-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(32, 201, 151, 0.2);
}

/* Image Portfolio */
.portfolio-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.portfolio-card:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(32, 201, 151, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: white;
    color: var(--secondar-color);
    transform: scale(1.1);
}

/* Contenu Portfolio */
.portfolio-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.portfolio-header h3 {
    font-size: 20px;
    color: var(--primary-color);
    font-weight: 600;
    line-height: 1.3;
    flex: 1;
}

.project-status {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.project-status.completed {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.project-status.in-progress {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.portfolio-description-text {
    color: var(--gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.portfolio-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 1.5rem;
}

.tech-tag {
    background: rgba(32, 201, 151, 0.1);
    color: var(--secondar-color);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(32, 201, 151, 0.2);
    transition: all 0.3s ease;
}

.tech-tag:hover {
    background: var(--secondar-color);
    color: white;
    transform: scale(1.05);
}

.portfolio-meta {
    display: flex;
    gap: 1.5rem;
    margin-top: auto;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray);
    font-size: 14px;
}

.meta-item i {
    color: var(--secondar-color);
    font-size: 16px;
}

/* Portfolio End */

/* Contact Section */
#contact{
    padding: 6rem 0;
    background: var(--bg-primary);
}

#contact hr {
    width: 100px;
    height: 3px;
    background-color: var(--secondar-color);
    border: none;
    margin: 5px auto 2rem;
}

/* Contact Layout 2 colonnes */
.contact-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
    align-items: start;
}

/* Informations de contact à gauche */
.contact-info-left {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    height: fit-content;
}

.contact-info-left h3 {
    font-size: 28px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.contact-description {
    color: var(--gray);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-cards {
    margin-bottom: 2rem;
}

.contact-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(32, 201, 151, 0.05);
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(32, 201, 151, 0.1);
}

.contact-card:hover {
    background: rgba(32, 201, 151, 0.1);
    border-color: var(--secondar-color);
    transform: translateX(5px);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-secondary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 20px;
    color: white;
}

.contact-card:hover .contact-icon {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
}

.contact-details h4 {
    font-size: 16px;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.contact-details p {
    color: var(--gray);
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

/* Réseaux sociaux */
.contact-social {
    border-top: 1px solid rgba(32, 201, 151, 0.2);
    padding-top: 2rem;
}

.contact-social h4 {
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 45px;
    height: 45px;
    background: var(--gradient-secondary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(32, 201, 151, 0.4);
}

.social-link i {
    font-size: 18px;
}

/* Contact Form */
.contact-form-container {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    height: fit-content;
}

.contact-form-container h3 {
    text-align: center;
    font-size: 28px;
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-weight: 600;
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 50px 15px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    background: #ffffff;
    transition: all 0.3s ease;
    outline: none;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--secondar-color);
    box-shadow: 0 0 0 3px rgba(32, 201, 151, 0.1);
    transform: translateY(-2px);
}

.form-group i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
    font-size: 18px;
    transition: all 0.3s ease;
}

.form-group input:focus + i,
.form-group textarea:focus + i {
    color: var(--secondar-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.btn-submit {
    width: 100%;
    padding: 15px;
    background: var(--gradient-secondary);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(32, 201, 151, 0.3);
    background: linear-gradient(45deg, #15a076 0%, #20c997 100%);
}

.btn-submit i {
    font-size: 16px;
}
/* Contrôles de navigation */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Toggle de thème */
.theme-toggle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

.theme-toggle i {
    color: #fff;
    font-size: 20px;
    transition: all 0.3s ease;
}

/* Menu mobile */
.menu-icon{
    display: none;
    width: 45px;
    height: 45px;
    border-radius: 8px;
    background: rgba(32, 201, 151, 0.1);
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-icon:hover {
    background: rgba(32, 201, 151, 0.2);
}

.menu-icon i{
    color: var(--primary-color);
    font-size: 24px;
    transition: all 0.3s ease;
}

.menu-icon:hover i {
    color: var(--secondar-color);
}
/* ===== RESPONSIVE DESIGN ===== */

/* Très grands écrans */
@media(min-width:1400px){
    .container {
        max-width: 1300px;
    }

    header .header-content {
        max-width: 1200px;
        gap: 4rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 3rem;
    }

    .skills-content .row {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 30px;
    }

    .btn-primary.enhanced {
        padding: 18px 36px;
        font-size: 19px;
    }

    /* Education responsive - Très grands écrans */
    #Education {
        padding: 8rem 0;
    }

    #Education h2 {
        font-size: 42px;
        margin-bottom: 3rem;
    }

    .education-timeline {
        max-width: 1000px;
    }

    .timeline-container::before {
        width: 6px;
    }

    .timeline-dot {
        width: 70px;
        height: 70px;
    }

    .timeline-dot i {
        font-size: 28px;
    }

    .education-card {
        padding: 2.5rem;
        border-radius: 25px;
    }

    .institution-logo {
        width: 90px;
        height: 90px;
    }

    .college-image {
        width: 90px;
        height: 90px;
    }

    .degree {
        font-size: 2.2rem;
    }

    .specialization {
        font-size: 1.4rem;
    }

    .institution {
        font-size: 1.1rem;
    }

    .detail-item {
        font-size: 16px;
    }

    .skills-acquired h5 {
        font-size: 18px;
    }

    .skill-tag {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Breakpoint intermédiaire pour tablettes */
@media(max-width:1024px){
    .container {
        max-width: 95%;
        padding: 0 2rem;
    }

    header .header-content {
        gap: 2rem;
        padding: 3rem 1rem 0;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }

    .skills-content .row {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 20px;
    }

    .stack-row {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }

    /* Education responsive - Tablettes larges */
    #Education {
        padding: 5rem 0;
    }

    #Education h2 {
        font-size: 36px;
    }

    .education-timeline {
        max-width: 800px;
        padding: 0 1.5rem;
    }

    .timeline-item:nth-child(odd) .timeline-content {
        padding-right: 2rem;
    }

    .timeline-item:nth-child(even) .timeline-content {
        padding-left: 2rem;
    }

    .education-card {
        padding: 2rem;
    }

    .institution-logo {
        width: 75px;
        height: 75px;
    }

    .college-image {
        width: 75px;
        height: 75px;
    }

    .degree {
        font-size: 2rem;
    }

    .specialization {
        font-size: 1.3rem;
    }
}

/* Tablettes et petits écrans */
@media(max-width:768px){
    nav ul{
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        flex-direction: column;
        text-align: left;
        background: var(--navbar-bg);
        color: var(--text-color);
        gap: 0;
        overflow: hidden;
        border-radius: 0 0 15px 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
        border-top: none;
        margin: 0;
        z-index: 1000;
        max-height: 0;
        opacity: 0;
        transition: all 0.3s ease;
    }

    nav ul.show{
        max-height: 400px;
        opacity: 1;
    }

    nav ul li{
        padding: 0;
        margin: 0;
        border-bottom: 1px solid var(--border-color);
        text-align: left;
        width: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    nav ul.show li{
        transform: translateX(0);
    }

    nav ul li:nth-child(1) { transition-delay: 0.1s; }
    nav ul li:nth-child(2) { transition-delay: 0.15s; }
    nav ul li:nth-child(3) { transition-delay: 0.2s; }
    nav ul li:nth-child(4) { transition-delay: 0.25s; }
    nav ul li:nth-child(5) { transition-delay: 0.3s; }

    nav ul li:last-child {
        border-bottom: none;
    }

    nav ul li a{
        display: block;
        padding: 15px 20px;
        text-align: left;
        color: var(--text-color);
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        font-size: 16px;
        width: 100%;
        box-sizing: border-box;
    }

    nav ul li a:hover {
        background: rgba(32, 201, 151, 0.1);
        color: var(--secondar-color);
    }

    .menu-icon{
        display: flex;
    }

    .nav-controls {
        gap: 8px;
    }

    .theme-toggle {
        width: 35px;
        height: 35px;
    }

    .theme-toggle i {
        font-size: 16px;
    }

    /* Réduire la hauteur de la navbar sur mobile */
    header .navbar nav {
        padding: 0.8rem 1.5rem;
        min-height: 50px;
        position: relative;
    }

    #home #logo {
        font-size: 20px;
    }
    #menuList{
        transition: all 0.5s;
    }
    header .header-content{
        grid-template-columns: 1fr;
        gap: 2rem;
        min-height: unset;
        padding: 2rem 1rem 0;
    }
    header .header-content .content-text{
        margin-bottom: 1rem;
        margin-top: 5rem;
    }
    header .header-content img{
        max-width: 380px;
        margin: auto;
    }
    #home .header-content h1{
        font-size: 50px;
    }

    /* Responsive pour nouveaux éléments */

    .skills-highlight {
        gap: 0.75rem;
        margin: 1.5rem 0;
    }

    .skill-badge {
        padding: 0.5rem 0.75rem;
        font-size: 13px;
    }

    .cta-buttons {
        gap: 0.75rem;
        margin-top: 1.5rem;
    }

    .btn-primary.enhanced {
        padding: 14px 28px;
        font-size: 16px;
        gap: 0.5rem;
    }

    .btn-icon {
        font-size: 18px;
    }
    
    #about .about-content{
        grid-template-columns: 1fr;
        row-gap: 3rem;
    }
    #skills .skills-content .row{
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 15px;
    }

    /* Portfolio responsive */
    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .portfolio-filters {
        gap: 0.75rem;
    }

    .filter-btn {
        padding: 10px 20px;
        font-size: 12px;
    }

    .portfolio-image {
        height: 200px;
    }

    .portfolio-content {
        padding: 1.5rem;
    }

    .portfolio-header h3 {
        font-size: 18px;
    }

    .action-btn {
        width: 45px;
        height: 45px;
    }

    .category-title {
        font-size: 20px;
        margin-bottom: 1rem;
    }

    .skill-category {
        margin-bottom: 2rem;
    }

    #skills .skills-content {
        padding: 1.5rem;
    }

    .stack-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stack-item {
        padding: 15px;
    }

    .stack-header h4 {
        font-size: 16px;
    }

    .tech-badge {
        font-size: 10px;
        padding: 3px 6px;
    }

    /* Responsive pour spécialités */
    .specialties-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .specialty-card {
        padding: 1.5rem;
    }

    .specialties-title {
        font-size: 28px;
    }

    .specialty-card {
        padding: 1.5rem;
    }

    .specialty-icon {
        width: 70px;
        height: 70px;
    }

    .specialty-icon i {
        font-size: 28px;
    }

    /* Education responsive - Tablettes */
    #Education {
        padding: 4rem 0;
    }

    #Education h2 {
        font-size: 32px;
        margin-bottom: 2rem;
    }

    .education-timeline {
        max-width: 100%;
        padding: 0 1rem;
    }

    /* Timeline responsive pour tablettes */
    .timeline-container::before {
        left: 30px;
        transform: none;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 80px;
        margin-right: 0;
        padding-left: 2rem;
        padding-right: 0;
    }

    .timeline-dot {
        left: 30px;
        transform: none;
        width: 50px;
        height: 50px;
    }

    .timeline-dot i {
        font-size: 20px;
    }

    .education-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .institution-logo {
        width: 60px;
        height: 60px;
    }

    .college-image {
        width: 60px;
        height: 60px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .card-badge {
        align-self: flex-end;
        margin-top: -2rem;
    }

    .degree {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }

    .specialization {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    .institution {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .education-details {
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .detail-item {
        font-size: 14px;
    }

    .skills-acquired {
        margin-top: 1rem;
    }

    .skills-acquired h5 {
        font-size: 16px;
        margin-bottom: 0.75rem;
    }

    .skill-tag {
        padding: 4px 8px;
        font-size: 11px;
        margin: 2px;
    }
    /* Contact responsive */
    .contact-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info-left {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .contact-info-left h3 {
        font-size: 24px;
    }

    .contact-form-container {
        padding: 2rem;
    }

    .contact-form-container h3 {
        font-size: 24px;
    }

    .contact-card {
        padding: 1rem;
        gap: 0.75rem;
    }

    .contact-icon {
        width: 40px;
        height: 40px;
    }

    .contact-icon i {
        font-size: 16px;
    }

    .social-links {
        gap: 0.75rem;
    }

    .social-link {
        width: 40px;
        height: 40px;
    }

    .social-link i {
        font-size: 16px;
    }

}
/* Breakpoint intermédiaire pour petits écrans */
@media(max-width:640px){
    .container {
        padding: 0 1.5rem;
    }

    header .header-content {
        padding: 1.5rem 1rem 0;
        gap: 1.5rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .skills-content .row {
        grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
        gap: 15px;
    }

    .btn-primary.enhanced {
        padding: 12px 24px;
        font-size: 16px;
        min-width: 180px;
    }

    /* Education responsive - Petits écrans */
    #Education {
        padding: 3rem 0;
    }

    #Education h2 {
        font-size: 28px;
        margin-bottom: 1.5rem;
    }

    .education-timeline {
        padding: 0 0.5rem;
    }

    .timeline-container::before {
        left: 20px;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 60px;
        padding-left: 1.5rem;
    }

    .timeline-dot {
        left: 20px;
        width: 40px;
        height: 40px;
        top: 1.5rem;
    }

    .timeline-dot i {
        font-size: 16px;
    }

    .education-card {
        padding: 1.25rem;
        border-radius: 15px;
    }

    .institution-logo {
        width: 50px;
        height: 50px;
    }

    .college-image {
        width: 50px;
        height: 50px;
    }

    .degree {
        font-size: 1.6rem;
    }

    .specialization {
        font-size: 1.1rem;
    }

    .institution {
        font-size: 0.95rem;
    }

    .card-badge {
        padding: 6px 12px;
        font-size: 10px;
    }

    .detail-item {
        font-size: 13px;
    }

    .skills-acquired h5 {
        font-size: 15px;
    }

    .skill-tag {
        padding: 3px 6px;
        font-size: 10px;
    }
}

/* Très petits écrans (mobiles) */
@media(max-width:528px){
    /* Optimisations pour très petits écrans */
    body {
        font-size: 14px;
        overflow-x: hidden;
    }

    .container {
        padding: 0 1rem;
        max-width: 100%;
    }

    #home .header-content h1{
        font-size: 28px;
        line-height: 1.2;
    }
    #home .header-content p span,
    #home .header-content .title{
        font-size: 14px;
    }
    .subtitle {
        font-size: 16px;
    }
    header .header-content img{
        margin-top: 1rem;
        max-width: 280px;
        width: 100%;
        height: auto;
    }

    /* Améliorer la navigation mobile */
    nav ul {
        top: 45px;
        max-width: 100vw;
        left: 0;
        right: 0;
        margin: 0;
    }

    nav ul li a {
        padding: 12px 20px;
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Mobile responsive pour nouveaux éléments */

    .skills-highlight {
        justify-content: center;
        gap: 0.5rem;
        margin: 1rem 0;
    }

    .skill-badge {
        padding: 0.4rem 0.6rem;
        font-size: 12px;
    }

    .skill-badge i {
        font-size: 14px;
    }

    .cta-buttons {
        gap: 0.75rem;
        margin-top: 1rem;
        justify-content: center;
    }

    .btn-primary.enhanced {
        padding: 12px 24px;
        font-size: 15px;
        gap: 0.5rem;
        text-align: center;
        justify-content: center;
        min-width: 200px;
    }

    .btn-text {
        font-size: 14px;
    }

    .btn-icon {
        font-size: 16px;
    }

    .floating-badge {
        top: 10px;
        right: 10px;
        padding: 0.4rem 0.6rem;
        font-size: 12px;
    }

    /* Image responsive sur mobile */
    .home__img {
        margin-top: 2rem;
    }

    .image-container {
        max-width: 300px;
        margin: 0 auto;
    }

    /* Réduire les animations sur mobile pour les performances */
    .animate-on-scroll {
        animation-duration: 0.5s;
    }

    /* Optimisations de performance mobile */
    * {
        -webkit-tap-highlight-color: transparent;
    }

    .btn-primary.enhanced {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        will-change: transform;
    }

    /* Améliorer l'accessibilité tactile */
    button, .btn-primary, .btn-secondary, .filter-btn {
        min-height: 44px;
        min-width: 44px;
    }

    /* Corriger les problèmes de viewport */
    html {
        overflow-x: hidden;
    }

    body {
        position: relative;
        overflow-x: hidden;
        width: 100%;
    }

    /* Optimiser les espacements */
    .container {
        padding: 0 1rem;
    }

    /* Navbar encore plus compacte sur très petits écrans */
    header .navbar nav {
        padding: 0.6rem 1rem;
        min-height: 45px;
    }

    #home #logo {
        font-size: 18px;
    }

    .theme-toggle {
        width: 32px;
        height: 32px;
    }

    .theme-toggle i {
        font-size: 14px;
    }

    .menu-icon {
        width: 35px;
        height: 35px;
    }

    .menu-icon i {
        font-size: 20px;
    }

    /* Corrections pour éviter le débordement horizontal */
    * {
        box-sizing: border-box;
    }

    .skills-highlight {
        flex-wrap: wrap;
        justify-content: center;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0;
    }

    .portfolio-card {
        max-width: 100%;
        margin: 0 auto;
    }

    /* Améliorer les formulaires sur mobile */
    .contact-form input,
    .contact-form textarea {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    /* Corriger les débordements de texte */
    h1, h2, h3, h4, h5, h6 {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Améliorer les cartes de compétences */
    .skill-category {
        margin-bottom: 1.5rem;
        padding: 0 0.5rem;
    }

    .skills-content .row {
        grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
        gap: 8px;
        padding: 0;
    }
    #about h2 ,
    #skills h2 ,
    #contact h2,
    #Education h2{
        font-size: 30px;
    }
    #Education h2{
        font-size: 40px;
    }
    #about .about-content .colon1 h3{
        font-size: 20px;
    }
    #skills .skills-content .row{
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 10px;
    }

    /* Portfolio très petits écrans */
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .portfolio-filters {
        gap: 0.5rem;
        justify-content: center;
    }

    .filter-btn {
        padding: 8px 16px;
        font-size: 11px;
    }

    .portfolio-image {
        height: 180px;
    }

    .portfolio-content {
        padding: 1rem;
    }

    .portfolio-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .portfolio-header h3 {
        font-size: 16px;
    }

    .portfolio-description-text {
        font-size: 14px;
    }

    .portfolio-meta {
        gap: 1rem;
        flex-wrap: wrap;
    }

    .meta-item {
        font-size: 12px;
    }

    .action-btn {
        width: 40px;
        height: 40px;
    }

    .portfolio-actions {
        gap: 0.75rem;
    }

    .category-title {
        font-size: 18px;
        margin-bottom: 0.8rem;
    }

    .category-title i {
        font-size: 16px;
        margin-right: 8px;
    }

    .skill-category {
        margin-bottom: 1.5rem;
    }

    #skills .skills-content {
        padding: 1rem;
    }

    .stack-item {
        padding: 12px;
    }

    .stack-header h4 {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .tech-badge {
        font-size: 9px;
        padding: 2px 5px;
    }

    .stack-technologies {
        gap: 5px;
        margin-bottom: 10px;
    }

    /* Responsive très petits écrans pour spécialités */
    .specialties-title {
        font-size: 24px;
        margin-bottom: 2rem;
    }

    .specialty-card {
        padding: 1rem;
    }

    .specialty-card h4 {
        font-size: 20px;
    }

    .specialty-card p {
        font-size: 14px;
    }

    .specialty-icon {
        width: 60px;
        height: 60px;
    }

    .specialty-icon i {
        font-size: 24px;
    }


        .education-list {
      flex-direction: column;
    }

    .college-info {
      flex-direction: column;
      align-items: center;
    }
    .college-details {
        padding: 15px;
    }

  
    .degree {
      font-size: 1.4rem;
    }
  
    .institution {
      font-size: 1rem;
    }
  
    .year {
      font-size: 0.8rem;
    }

    /* Contact très petits écrans */
    .contact-info-left {
        padding: 1.5rem;
    }

    .contact-info-left h3 {
        font-size: 20px;
    }

    .contact-description {
        font-size: 14px;
    }

    .contact-form-container {
        padding: 1.5rem;
    }

    .contact-form-container h3 {
        font-size: 20px;
        margin-bottom: 1.5rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 12px 40px 12px 12px;
        font-size: 14px;
    }

    .form-group i {
        font-size: 16px;
        right: 12px;
    }

    .btn-submit {
        padding: 12px;
        font-size: 16px;
    }

    .contact-card {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .contact-details h4 {
        font-size: 14px;
    }

    .contact-details p {
        font-size: 12px;
    }

    .contact-icon {
        width: 35px;
        height: 35px;
    }

    .contact-icon i {
        font-size: 14px;
    }

    .social-link {
        width: 35px;
        height: 35px;
    }

    .social-link i {
        font-size: 14px;
    }

    /* Education responsive - Très petits écrans (mobiles) */
    #Education {
        padding: 2.5rem 0;
    }

    #Education h2 {
        font-size: 24px;
        margin-bottom: 1rem;
    }

    #Education hr {
        width: 60px;
        height: 2px;
        margin: 5px auto 2rem;
    }

    .education-timeline {
        padding: 0;
        max-width: 100%;
    }

    /* Timeline mobile - Version simplifiée */
    .timeline-container::before {
        left: 15px;
        width: 2px;
    }

    .timeline-item {
        margin-bottom: 2.5rem;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 50px;
        padding-left: 1rem;
        padding-right: 0.5rem;
    }

    .timeline-dot {
        left: 15px;
        width: 30px;
        height: 30px;
        top: 1rem;
    }

    .timeline-dot i {
        font-size: 12px;
    }

    .timeline-dot.current {
        box-shadow: 0 0 0 4px rgba(32, 201, 151, 0.2);
    }

    .timeline-dot.completed {
        box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
    }

    .education-card {
        padding: 1rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
    }

    .card-header {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        gap: 0.5rem;
    }

    .institution-logo {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
    }

    .college-image {
        width: 40px;
        height: 40px;
    }

    .card-badge {
        padding: 4px 8px;
        font-size: 9px;
        margin-top: 0;
        align-self: center;
        white-space: nowrap;
    }

    .degree {
        font-size: 1.3rem;
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }

    .specialization {
        font-size: 1rem;
        line-height: 1.3;
        margin-bottom: 0.5rem;
    }

    .institution {
        font-size: 0.85rem;
        line-height: 1.3;
        margin-bottom: 0.75rem;
    }

    .education-details {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .detail-item {
        font-size: 12px;
        gap: 6px;
    }

    .detail-item i {
        font-size: 11px;
        width: 12px;
        flex-shrink: 0;
    }

    .skills-acquired {
        margin-top: 0.75rem;
    }

    .skills-acquired h5 {
        font-size: 14px;
        margin-bottom: 0.5rem;
    }

    .skills-list {
        gap: 0.25rem;
    }

    .skill-tag {
        padding: 2px 4px;
        font-size: 9px;
        margin: 1px;
        border-radius: 8px;
    }

    /* Optimisations pour très petits écrans */
    .education-card:hover {
        transform: translateY(-5px);
    }

    .timeline-item {
        transform: translateY(20px);
    }

    .timeline-item.animate {
        transform: translateY(0);
    }
 
}

.education-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
.education-item {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }
  
/* College Info Section */
  .college-info {
    display: flex;
    align-items: center;
    gap: 20px;
}
  

  
.college-details {
    display: flex;
    flex-direction: column;
    padding: 20px;
}
  
.degree {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 10px;
}
  
.institution {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 5px;
}
  
.year {
    font-size: 1rem;
    color: var(--primary-color);
}
  

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--secondar-color), #15a076);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, #15a076, var(--secondar-color));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(32, 201, 151, 0.4);
}

.scroll-to-top:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);
}

.scroll-to-top i {
    transition: transform 0.3s ease;
}

.scroll-to-top:hover i {
    transform: translateY(-2px);
}

/* Barre de progression circulaire pour le bouton scroll */
.scroll-to-top {
    --progress: 0deg;
}

.scroll-to-top::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid transparent;
    border-radius: 50%;
    background: conic-gradient(var(--secondar-color) var(--progress), rgba(255, 255, 255, 0.2) var(--progress));
    transition: all 0.3s ease;
    z-index: -1;
    opacity: 0;
}

.scroll-to-top.progress::after {
    opacity: 0.8;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

/* Responsive pour le bouton scroll */
@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

@media (max-width: 528px) {
    .scroll-to-top {
        bottom: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

  /* ===== FOOTER =====*/
.footer{
    background-color: var(--primary-color);
    color: #fff;
    text-align: center;
    font-weight: 600;
    padding: 2rem 0;

}
.footer__title{
    font-size: 2rem;
    margin-bottom: 1rem;
}
.footer__copy{
    margin-top: 1rem;
    font-size: .938rem;
}
.footer a{
font-size: 1.5rem;
color: #ffffff;
margin: 0 1rem;
}


.btn {
    height: 35px;
    width: 35px;
    background: var(--secondar-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    right: 20px;
    bottom: 20px;
    cursor: pointer;
}
.icone {
    width: 30px;
}
#Education{
    margin-top: 8rem;
}