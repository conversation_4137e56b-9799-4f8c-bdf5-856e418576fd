/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { setActiveConsumer } from '@angular/core/primitives/signals';
import { hasInSkipHydrationBlockFlag } from '../hydration/skip_hydration';
import { assertDefined } from '../util/assert';
import { assertLContainer, assertLView, assertTNodeForLView } from './assert';
import { renderView } from './instructions/render';
import { createLView } from './instructions/shared';
import { CONTAINER_HEADER_OFFSET, NATIVE } from './interfaces/container';
import { DECLARATION_LCONTAINER, FLAGS, HYDRATION, QUERIES, RENDERER, T_HOST, TVIEW } from './interfaces/view';
import { addViewToDOM, destroyLView, detachView, getBeforeNodeForView, insertView, nativeParentNode } from './node_manipulation';
export function createAndRenderEmbeddedLView(declarationLView, templateTNode, context, options) {
    const prevConsumer = setActiveConsumer(null);
    try {
        const embeddedTView = templateTNode.tView;
        ngDevMode && assertDefined(embeddedTView, 'TView must be defined for a template node.');
        ngDevMode && assertTNodeForLView(templateTNode, declarationLView);
        // Embedded views follow the change detection strategy of the view they're declared in.
        const isSignalView = declarationLView[FLAGS] & 4096 /* LViewFlags.SignalView */;
        const viewFlags = isSignalView ? 4096 /* LViewFlags.SignalView */ : 16 /* LViewFlags.CheckAlways */;
        const embeddedLView = createLView(declarationLView, embeddedTView, context, viewFlags, null, templateTNode, null, null, options?.injector ?? null, options?.embeddedViewInjector ?? null, options?.dehydratedView ?? null);
        const declarationLContainer = declarationLView[templateTNode.index];
        ngDevMode && assertLContainer(declarationLContainer);
        embeddedLView[DECLARATION_LCONTAINER] = declarationLContainer;
        const declarationViewLQueries = declarationLView[QUERIES];
        if (declarationViewLQueries !== null) {
            embeddedLView[QUERIES] = declarationViewLQueries.createEmbeddedView(embeddedTView);
        }
        // execute creation mode of a view
        renderView(embeddedTView, embeddedLView, context);
        return embeddedLView;
    }
    finally {
        setActiveConsumer(prevConsumer);
    }
}
export function getLViewFromLContainer(lContainer, index) {
    const adjustedIndex = CONTAINER_HEADER_OFFSET + index;
    // avoid reading past the array boundaries
    if (adjustedIndex < lContainer.length) {
        const lView = lContainer[adjustedIndex];
        ngDevMode && assertLView(lView);
        return lView;
    }
    return undefined;
}
/**
 * Returns whether an elements that belong to a view should be
 * inserted into the DOM. For client-only cases, DOM elements are
 * always inserted. For hydration cases, we check whether serialized
 * info is available for a view and the view is not in a "skip hydration"
 * block (in which case view contents was re-created, thus needing insertion).
 */
export function shouldAddViewToDom(tNode, dehydratedView) {
    return !dehydratedView || dehydratedView.firstChild === null ||
        hasInSkipHydrationBlockFlag(tNode);
}
export function addLViewToLContainer(lContainer, lView, index, addToDOM = true) {
    const tView = lView[TVIEW];
    // Insert into the view tree so the new view can be change-detected
    insertView(tView, lView, lContainer, index);
    // Insert elements that belong to this view into the DOM tree
    if (addToDOM) {
        const beforeNode = getBeforeNodeForView(index, lContainer);
        const renderer = lView[RENDERER];
        const parentRNode = nativeParentNode(renderer, lContainer[NATIVE]);
        if (parentRNode !== null) {
            addViewToDOM(tView, lContainer[T_HOST], renderer, lView, parentRNode, beforeNode);
        }
    }
    // When in hydration mode, reset the pointer to the first child in
    // the dehydrated view. This indicates that the view was hydrated and
    // further attaching/detaching should work with this view as normal.
    const hydrationInfo = lView[HYDRATION];
    if (hydrationInfo !== null && hydrationInfo.firstChild !== null) {
        hydrationInfo.firstChild = null;
    }
}
export function removeLViewFromLContainer(lContainer, index) {
    const lView = detachView(lContainer, index);
    if (lView !== undefined) {
        destroyLView(lView[TVIEW], lView);
    }
    return lView;
}
//# sourceMappingURL=data:application/json;base64,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