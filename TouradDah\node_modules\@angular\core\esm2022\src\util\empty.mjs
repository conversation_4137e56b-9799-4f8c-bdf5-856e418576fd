/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { initNgDevMode } from './ng_dev_mode';
/**
 * This file contains reuseable "empty" symbols that can be used as default return values
 * in different parts of the rendering code. Because the same symbols are returned, this
 * allows for identity checks against these values to be consistently used by the framework
 * code.
 */
export const EMPTY_OBJ = {};
export const EMPTY_ARRAY = [];
// freezing the values prevents any code from accidentally inserting new values in
if ((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode()) {
    // These property accesses can be ignored because ngDevMode will be set to false
    // when optimizing code and the whole if statement will be dropped.
    // tslint:disable-next-line:no-toplevel-property-access
    Object.freeze(EMPTY_OBJ);
    // tslint:disable-next-line:no-toplevel-property-access
    Object.freeze(EMPTY_ARRAY);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZW1wdHkuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy91dGlsL2VtcHR5LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUNILE9BQU8sRUFBQyxhQUFhLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFFNUM7Ozs7O0dBS0c7QUFFSCxNQUFNLENBQUMsTUFBTSxTQUFTLEdBQVUsRUFBVyxDQUFDO0FBQzVDLE1BQU0sQ0FBQyxNQUFNLFdBQVcsR0FBVSxFQUFFLENBQUM7QUFFckMsa0ZBQWtGO0FBQ2xGLElBQUksQ0FBQyxPQUFPLFNBQVMsS0FBSyxXQUFXLElBQUksU0FBUyxDQUFDLElBQUksYUFBYSxFQUFFLEVBQUUsQ0FBQztJQUN2RSxnRkFBZ0Y7SUFDaEYsbUVBQW1FO0lBQ25FLHVEQUF1RDtJQUN2RCxNQUFNLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxDQUFDO0lBQ3pCLHVEQUF1RDtJQUN2RCxNQUFNLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDO0FBQzdCLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cbmltcG9ydCB7aW5pdE5nRGV2TW9kZX0gZnJvbSAnLi9uZ19kZXZfbW9kZSc7XG5cbi8qKlxuICogVGhpcyBmaWxlIGNvbnRhaW5zIHJldXNlYWJsZSBcImVtcHR5XCIgc3ltYm9scyB0aGF0IGNhbiBiZSB1c2VkIGFzIGRlZmF1bHQgcmV0dXJuIHZhbHVlc1xuICogaW4gZGlmZmVyZW50IHBhcnRzIG9mIHRoZSByZW5kZXJpbmcgY29kZS4gQmVjYXVzZSB0aGUgc2FtZSBzeW1ib2xzIGFyZSByZXR1cm5lZCwgdGhpc1xuICogYWxsb3dzIGZvciBpZGVudGl0eSBjaGVja3MgYWdhaW5zdCB0aGVzZSB2YWx1ZXMgdG8gYmUgY29uc2lzdGVudGx5IHVzZWQgYnkgdGhlIGZyYW1ld29ya1xuICogY29kZS5cbiAqL1xuXG5leHBvcnQgY29uc3QgRU1QVFlfT0JKOiBuZXZlciA9IHt9IGFzIG5ldmVyO1xuZXhwb3J0IGNvbnN0IEVNUFRZX0FSUkFZOiBhbnlbXSA9IFtdO1xuXG4vLyBmcmVlemluZyB0aGUgdmFsdWVzIHByZXZlbnRzIGFueSBjb2RlIGZyb20gYWNjaWRlbnRhbGx5IGluc2VydGluZyBuZXcgdmFsdWVzIGluXG5pZiAoKHR5cGVvZiBuZ0Rldk1vZGUgPT09ICd1bmRlZmluZWQnIHx8IG5nRGV2TW9kZSkgJiYgaW5pdE5nRGV2TW9kZSgpKSB7XG4gIC8vIFRoZXNlIHByb3BlcnR5IGFjY2Vzc2VzIGNhbiBiZSBpZ25vcmVkIGJlY2F1c2UgbmdEZXZNb2RlIHdpbGwgYmUgc2V0IHRvIGZhbHNlXG4gIC8vIHdoZW4gb3B0aW1pemluZyBjb2RlIGFuZCB0aGUgd2hvbGUgaWYgc3RhdGVtZW50IHdpbGwgYmUgZHJvcHBlZC5cbiAgLy8gdHNsaW50OmRpc2FibGUtbmV4dC1saW5lOm5vLXRvcGxldmVsLXByb3BlcnR5LWFjY2Vzc1xuICBPYmplY3QuZnJlZXplKEVNUFRZX09CSik7XG4gIC8vIHRzbGludDpkaXNhYmxlLW5leHQtbGluZTpuby10b3BsZXZlbC1wcm9wZXJ0eS1hY2Nlc3NcbiAgT2JqZWN0LmZyZWV6ZShFTVBUWV9BUlJBWSk7XG59XG4iXX0=