/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵɵinject as inject } from '../../di/injector_compatibility';
import { ɵɵdefineInjectable as defineInjectable } from '../../di/interface/defs';
import { internalImportProvidersFrom } from '../../di/provider_collection';
import { EnvironmentInjector } from '../../di/r3_injector';
import { performanceMarkFeature } from '../../util/performance';
import { createEnvironmentInjector } from '../ng_module_ref';
/**
 * A service used by the framework to create instances of standalone injectors. Those injectors are
 * created on demand in case of dynamic component instantiation and contain ambient providers
 * collected from the imports graph rooted at a given standalone component.
 */
class StandaloneService {
    constructor(_injector) {
        this._injector = _injector;
        this.cachedInjectors = new Map();
    }
    getOrCreateStandaloneInjector(componentDef) {
        if (!componentDef.standalone) {
            return null;
        }
        if (!this.cachedInjectors.has(componentDef)) {
            const providers = internalImportProvidersFrom(false, componentDef.type);
            const standaloneInjector = providers.length > 0 ?
                createEnvironmentInjector([providers], this._injector, `Standalone[${componentDef.type.name}]`) :
                null;
            this.cachedInjectors.set(componentDef, standaloneInjector);
        }
        return this.cachedInjectors.get(componentDef);
    }
    ngOnDestroy() {
        try {
            for (const injector of this.cachedInjectors.values()) {
                if (injector !== null) {
                    injector.destroy();
                }
            }
        }
        finally {
            this.cachedInjectors.clear();
        }
    }
    /** @nocollapse */
    static { this.ɵprov = defineInjectable({
        token: StandaloneService,
        providedIn: 'environment',
        factory: () => new StandaloneService(inject(EnvironmentInjector)),
    }); }
}
/**
 * A feature that acts as a setup code for the {@link StandaloneService}.
 *
 * The most important responsibility of this feature is to expose the "getStandaloneInjector"
 * function (an entry points to a standalone injector creation) on a component definition object. We
 * go through the features infrastructure to make sure that the standalone injector creation logic
 * is tree-shakable and not included in applications that don't use standalone components.
 *
 * @codeGenApi
 */
export function ɵɵStandaloneFeature(definition) {
    performanceMarkFeature('NgStandalone');
    definition.getStandaloneInjector = (parentInjector) => {
        return parentInjector.get(StandaloneService).getOrCreateStandaloneInjector(definition);
    };
}
//# sourceMappingURL=data:application/json;base64,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