body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    background-color: #f4f4f4;
}

h1, h2, h3, h4 {
    color: #2c3e50;
}

a {
    text-decoration: none;
    color: #3498db;
}

a:hover {
    text-decoration: underline;
}

.container {
    width: 80%;
    margin: auto;
    overflow: hidden;
}

.header {
    background: #35424a;
    color: #ffffff;
    padding: 20px 0;
}

.footer {
    background: #35424a;
    color: #ffffff;
    text-align: center;
    padding: 10px 0;
}

.btn-primary {
    background: #3498db;
    color: #ffffff;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.btn-primary:hover {
    background: #2980b9;
}

.skill-progress {
    background: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    background: #3498db;
    height: 100%;
    transition: width 0.5s;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.portfolio-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    transition: transform 0.3s;
}

.portfolio-card:hover {
    transform: scale(1.05);
}

.portfolio-image img {
    width: 100%;
    height: auto;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}