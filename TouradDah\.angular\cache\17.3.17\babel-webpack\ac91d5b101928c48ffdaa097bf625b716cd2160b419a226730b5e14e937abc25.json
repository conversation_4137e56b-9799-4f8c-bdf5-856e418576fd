{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport { NavbarComponent } from './components/navbar/navbar.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { ScrollToTopComponent } from './components/scroll-to-top/scroll-to-top.component';\nimport * as i0 from \"@angular/core\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Tourad Dah - Portfolio';\n  }\n  ngOnInit() {\n    this.loadTheme();\n    this.initScrollAnimations();\n  }\n  onWindowScroll() {\n    this.updateScrollProgress();\n    this.animateOnScroll();\n  }\n  loadTheme() {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme === 'dark') {\n      document.body.setAttribute('data-theme', 'dark');\n    }\n  }\n  updateScrollProgress() {\n    const scrollProgress = document.querySelector('.scroll-progress');\n    if (scrollProgress) {\n      const scrollTop = window.pageYOffset;\n      const docHeight = document.body.scrollHeight - window.innerHeight;\n      const scrollPercent = scrollTop / docHeight * 100;\n      scrollProgress.style.width = scrollPercent + '%';\n    }\n  }\n  initScrollAnimations() {\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n    // Observer sera appliqué aux éléments après le rendu\n    setTimeout(() => {\n      const animatedElements = document.querySelectorAll('.animate-on-scroll');\n      animatedElements.forEach(el => observer.observe(el));\n    }, 100);\n  }\n  animateOnScroll() {\n    const elements = document.querySelectorAll('.animate-on-scroll');\n    elements.forEach(element => {\n      const elementTop = element.getBoundingClientRect().top;\n      const elementVisible = 150;\n      if (elementTop < window.innerHeight - elementVisible) {\n        element.classList.add('visible');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      hostBindings: function AppComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function AppComponent_scroll_HostBindingHandler() {\n            return ctx.onWindowScroll();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"app-container\"], [1, \"main-content\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-navbar\");\n          i0.ɵɵelementStart(2, \"main\", 1);\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"app-footer\")(5, \"app-scroll-to-top\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet, RouterModule, NavbarComponent, FooterComponent, ScrollToTopComponent],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: 80px;\\n}\\n\\n.scroll-progress[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 0%;\\n  height: 3px;\\n  background: var(--gradient-primary);\\n  z-index: 9999;\\n  transition: width 0.3s ease;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUFDRjs7QUFFQTtFQUNFLE9BQUE7RUFDQSxpQkFBQTtBQUNGOztBQUdBO0VBQ0UsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7RUFDQSxtQ0FBQTtFQUNBLGFBQUE7RUFDQSwyQkFBQTtBQUFGIiwic291cmNlc0NvbnRlbnQiOlsiLmFwcC1jb250YWluZXIge1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLm1haW4tY29udGVudCB7XG4gIGZsZXg6IDE7XG4gIHBhZGRpbmctdG9wOiA4MHB4OyAvLyBFc3BhY2UgcG91ciBsYSBuYXZiYXIgZml4ZVxufVxuXG4vLyBCYXJyZSBkZSBwcm9ncmVzc2lvbiBkdSBzY3JvbGxcbi5zY3JvbGwtcHJvZ3Jlc3Mge1xuICBwb3NpdGlvbjogZml4ZWQ7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgd2lkdGg6IDAlO1xuICBoZWlnaHQ6IDNweDtcbiAgYmFja2dyb3VuZDogdmFyKC0tZ3JhZGllbnQtcHJpbWFyeSk7XG4gIHotaW5kZXg6IDk5OTk7XG4gIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "RouterModule", "NavbarComponent", "FooterComponent", "ScrollToTopComponent", "AppComponent", "constructor", "title", "ngOnInit", "loadTheme", "initScrollAnimations", "onWindowScroll", "updateScrollProgress", "animateOnScroll", "savedTheme", "localStorage", "getItem", "document", "body", "setAttribute", "scrollProgress", "querySelector", "scrollTop", "window", "pageYOffset", "doc<PERSON><PERSON>ght", "scrollHeight", "innerHeight", "scrollPercent", "style", "width", "observerOptions", "threshold", "rootMargin", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "classList", "add", "setTimeout", "animatedElements", "querySelectorAll", "el", "observe", "elements", "element", "elementTop", "getBoundingClientRect", "top", "elementVisible", "selectors", "hostBindings", "AppComponent_HostBindings", "rf", "ctx", "i0", "ɵɵlistener", "AppComponent_scroll_HostBindingHandler", "ɵɵresolveWindow", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport { NavbarComponent } from './components/navbar/navbar.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport { ScrollToTopComponent } from './components/scroll-to-top/scroll-to-top.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [\n    CommonModule, \n    RouterOutlet, \n    RouterModule,\n    NavbarComponent,\n    FooterComponent,\n    ScrollToTopComponent\n  ],\n  template: `\n    <div class=\"app-container\">\n      <app-navbar></app-navbar>\n      <main class=\"main-content\">\n        <router-outlet></router-outlet>\n      </main>\n      <app-footer></app-footer>\n      <app-scroll-to-top></app-scroll-to-top>\n    </div>\n  `,\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'Tourad Dah - Portfolio';\n\n  ngOnInit() {\n    this.loadTheme();\n    this.initScrollAnimations();\n  }\n\n  @HostListener('window:scroll', [])\n  onWindowScroll() {\n    this.updateScrollProgress();\n    this.animateOnScroll();\n  }\n\n  private loadTheme() {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme === 'dark') {\n      document.body.setAttribute('data-theme', 'dark');\n    }\n  }\n\n  private updateScrollProgress() {\n    const scrollProgress = document.querySelector('.scroll-progress') as HTMLElement;\n    if (scrollProgress) {\n      const scrollTop = window.pageYOffset;\n      const docHeight = document.body.scrollHeight - window.innerHeight;\n      const scrollPercent = (scrollTop / docHeight) * 100;\n      scrollProgress.style.width = scrollPercent + '%';\n    }\n  }\n\n  private initScrollAnimations() {\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    // Observer sera appliqué aux éléments après le rendu\n    setTimeout(() => {\n      const animatedElements = document.querySelectorAll('.animate-on-scroll');\n      animatedElements.forEach(el => observer.observe(el));\n    }, 100);\n  }\n\n  private animateOnScroll() {\n    const elements = document.querySelectorAll('.animate-on-scroll');\n    elements.forEach(element => {\n      const elementTop = element.getBoundingClientRect().top;\n      const elementVisible = 150;\n      \n      if (elementTop < window.innerHeight - elementVisible) {\n        element.classList.add('visible');\n      }\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,YAAY,QAAQ,iBAAiB;AAC5D,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,oBAAoB,QAAQ,oDAAoD;;AAyBzF,OAAM,MAAOC,YAAY;EAvBzBC,YAAA;IAwBE,KAAAC,KAAK,GAAG,wBAAwB;;EAEhCC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQJ,SAASA,CAAA;IACf,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,KAAK,MAAM,EAAE;MACzBG,QAAQ,CAACC,IAAI,CAACC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;;EAEpD;EAEQP,oBAAoBA,CAAA;IAC1B,MAAMQ,cAAc,GAAGH,QAAQ,CAACI,aAAa,CAAC,kBAAkB,CAAgB;IAChF,IAAID,cAAc,EAAE;MAClB,MAAME,SAAS,GAAGC,MAAM,CAACC,WAAW;MACpC,MAAMC,SAAS,GAAGR,QAAQ,CAACC,IAAI,CAACQ,YAAY,GAAGH,MAAM,CAACI,WAAW;MACjE,MAAMC,aAAa,GAAIN,SAAS,GAAGG,SAAS,GAAI,GAAG;MACnDL,cAAc,CAACS,KAAK,CAACC,KAAK,GAAGF,aAAa,GAAG,GAAG;;EAEpD;EAEQlB,oBAAoBA,CAAA;IAC1B,MAAMqB,eAAe,GAAG;MACtBC,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;KACb;IAED,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAI;MACpDA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;QACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;;MAEzC,CAAC,CAAC;IACJ,CAAC,EAAEX,eAAe,CAAC;IAEnB;IACAY,UAAU,CAAC,MAAK;MACd,MAAMC,gBAAgB,GAAG3B,QAAQ,CAAC4B,gBAAgB,CAAC,oBAAoB,CAAC;MACxED,gBAAgB,CAACP,OAAO,CAACS,EAAE,IAAIZ,QAAQ,CAACa,OAAO,CAACD,EAAE,CAAC,CAAC;IACtD,CAAC,EAAE,GAAG,CAAC;EACT;EAEQjC,eAAeA,CAAA;IACrB,MAAMmC,QAAQ,GAAG/B,QAAQ,CAAC4B,gBAAgB,CAAC,oBAAoB,CAAC;IAChEG,QAAQ,CAACX,OAAO,CAACY,OAAO,IAAG;MACzB,MAAMC,UAAU,GAAGD,OAAO,CAACE,qBAAqB,EAAE,CAACC,GAAG;MACtD,MAAMC,cAAc,GAAG,GAAG;MAE1B,IAAIH,UAAU,GAAG3B,MAAM,CAACI,WAAW,GAAG0B,cAAc,EAAE;QACpDJ,OAAO,CAACR,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;;IAEpC,CAAC,CAAC;EACJ;;;uBA9DWrC,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAiD,SAAA;MAAAC,YAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAZE,EAAA,CAAAC,UAAA,oBAAAC,uCAAA;YAAA,OAAAH,GAAA,CAAA/C,cAAA,EAAgB;UAAA,UAAAgD,EAAA,CAAAG,eAAA,CAAJ;;;;;;;;;;UAXrBH,EAAA,CAAAI,cAAA,aAA2B;UACzBJ,EAAA,CAAAK,SAAA,iBAAyB;UACzBL,EAAA,CAAAI,cAAA,cAA2B;UACzBJ,EAAA,CAAAK,SAAA,oBAA+B;UACjCL,EAAA,CAAAM,YAAA,EAAO;UAEPN,EADA,CAAAK,SAAA,iBAAyB,wBACc;UACzCL,EAAA,CAAAM,YAAA,EAAM;;;qBAfNlE,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,eAAe,EACfC,oBAAoB;MAAA8D,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}