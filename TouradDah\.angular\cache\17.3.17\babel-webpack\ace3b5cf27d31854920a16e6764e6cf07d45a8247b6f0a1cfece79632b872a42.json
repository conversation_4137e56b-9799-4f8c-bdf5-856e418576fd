{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class ScrollToTopComponent {\n  constructor() {\n    this.isVisible = false;\n  }\n  onWindowScroll() {\n    this.isVisible = window.pageYOffset > 300;\n  }\n  scrollToTop() {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  static {\n    this.ɵfac = function ScrollToTopComponent_Factory(t) {\n      return new (t || ScrollToTopComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ScrollToTopComponent,\n      selectors: [[\"app-scroll-to-top\"]],\n      hostBindings: function ScrollToTopComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function ScrollToTopComponent_scroll_HostBindingHandler() {\n            return ctx.onWindowScroll();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"title\", \"Retour en haut\", 1, \"scroll-to-top\", 3, \"click\"], [1, \"fas\", \"fa-chevron-up\"]],\n      template: function ScrollToTopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function ScrollToTopComponent_Template_button_click_0_listener() {\n            return ctx.scrollToTop();\n          });\n          i0.ɵɵelement(1, \"i\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"visible\", ctx.isVisible);\n        }\n      },\n      dependencies: [CommonModule],\n      styles: [\".scroll-to-top[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 30px;\\n  right: 30px;\\n  width: 50px;\\n  height: 50px;\\n  background: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);\\n  transition: all 0.3s ease;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(20px);\\n  z-index: 1000;\\n}\\n.scroll-to-top.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n.scroll-to-top[_ngcontent-%COMP%]:hover {\\n  background: var(--secondar-color);\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(32, 201, 151, 0.4);\\n}\\n.scroll-to-top[_ngcontent-%COMP%]:active {\\n  transform: translateY(-2px);\\n}\\n.scroll-to-top[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.scroll-to-top[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateY(-2px);\\n}\\n\\n@media (max-width: 768px) {\\n  .scroll-to-top[_ngcontent-%COMP%] {\\n    bottom: 20px;\\n    right: 20px;\\n    width: 45px;\\n    height: 45px;\\n    font-size: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .scroll-to-top[_ngcontent-%COMP%] {\\n    bottom: 15px;\\n    right: 15px;\\n    width: 40px;\\n    height: 40px;\\n    font-size: 14px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ScrollToTopComponent", "constructor", "isVisible", "onWindowScroll", "window", "pageYOffset", "scrollToTop", "scrollTo", "top", "behavior", "selectors", "hostBindings", "ScrollToTopComponent_HostBindings", "rf", "ctx", "i0", "ɵɵlistener", "ScrollToTopComponent_scroll_HostBindingHandler", "ɵɵresolveWindow", "ɵɵelementStart", "ScrollToTopComponent_Template_button_click_0_listener", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\scroll-to-top\\scroll-to-top.component.ts"], "sourcesContent": ["import { Component, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-scroll-to-top',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <button \n      class=\"scroll-to-top\"\n      [class.visible]=\"isVisible\"\n      (click)=\"scrollToTop()\"\n      title=\"Retour en haut\">\n      <i class=\"fas fa-chevron-up\"></i>\n    </button>\n  `,\n  styleUrls: ['./scroll-to-top.component.scss']\n})\nexport class ScrollToTopComponent {\n  isVisible = false;\n\n  @HostListener('window:scroll', [])\n  onWindowScroll() {\n    this.isVisible = window.pageYOffset > 300;\n  }\n\n  scrollToTop() {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAiB9C,OAAM,MAAOC,oBAAoB;EAfjCC,YAAA;IAgBE,KAAAC,SAAS,GAAG,KAAK;;EAGjBC,cAAcA,CAAA;IACZ,IAAI,CAACD,SAAS,GAAGE,MAAM,CAACC,WAAW,GAAG,GAAG;EAC3C;EAEAC,WAAWA,CAAA;IACTF,MAAM,CAACG,QAAQ,CAAC;MACdC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE;KACX,CAAC;EACJ;;;uBAbWT,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAU,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAApBE,EAAA,CAAAC,UAAA,oBAAAC,+CAAA;YAAA,OAAAH,GAAA,CAAAX,cAAA,EAAgB;UAAA,UAAAY,EAAA,CAAAG,eAAA,CAAI;;;;;;;;;;UAV7BH,EAAA,CAAAI,cAAA,gBAIyB;UADvBJ,EAAA,CAAAC,UAAA,mBAAAI,sDAAA;YAAA,OAASN,GAAA,CAAAR,WAAA,EAAa;UAAA,EAAC;UAEvBS,EAAA,CAAAM,SAAA,WAAiC;UACnCN,EAAA,CAAAO,YAAA,EAAS;;;UAJPP,EAAA,CAAAQ,WAAA,YAAAT,GAAA,CAAAZ,SAAA,CAA2B;;;qBAJrBH,YAAY;MAAAyB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}