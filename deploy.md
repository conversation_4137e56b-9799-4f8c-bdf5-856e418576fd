# Guide de déploiement sur Vercel

## 📋 Étapes pour déployer sur Vercel

### 1. Préparation des fichiers
✅ `vercel.json` - Configuration de routage
✅ `package.json` - Métadonnées du projet  
✅ `README.md` - Documentation
✅ `.gitignore` - Fichiers à ignorer
✅ Optimisations HTML/CSS/JS

### 2. Déploiement via GitHub (Recommandé)

1. **Créer un repository GitHub** :
   ```bash
   git init
   git add .
   git commit -m "Initial commit: Portfolio Tourad Dah"
   git branch -M main
   git remote add origin https://github.com/VOTRE_USERNAME/portfolio.git
   git push -u origin main
   ```

2. **Connecter à Vercel** :
   - Aller sur [vercel.com](https://vercel.com)
   - Se connecter avec GitHub
   - Cliquer "New Project"
   - Sélectionner votre repository
   - Déployer automatiquement

### 3. Déploiement via Vercel CLI

1. **Installer Vercel CLI** :
   ```bash
   npm i -g vercel
   ```

2. **Déployer** :
   ```bash
   vercel
   ```

### 4. Configuration automatique

Le fichier `vercel.json` configure automatiquement :
- ✅ Routage SPA (Single Page Application)
- ✅ Headers de sécurité
- ✅ Cache optimisé pour les images
- ✅ Redirection vers index.html

### 5. Vérifications post-déploiement

- [ ] Navigation entre sections fonctionne
- [ ] Images se chargent correctement
- [ ] Formulaire de contact opérationnel
- [ ] Responsive design sur mobile
- [ ] Performance optimale

## 🔧 Résolution des problèmes courants

### Problème : "Seulement la page home s'affiche"
**Solution** : Le fichier `vercel.json` redirige toutes les routes vers `index.html`

### Problème : "Images ne se chargent pas"
**Solution** : Vérifier les chemins relatifs dans le HTML

### Problème : "Navigation ne fonctionne pas"
**Solution** : Le JavaScript gère maintenant la navigation smooth scroll

## 📱 Test final

Après déploiement, tester :
1. Navigation menu → sections
2. Boutons "Découvrir mes projets"
3. Filtres portfolio
4. Formulaire contact
5. Responsive mobile/desktop
