{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n  const startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n  return operate((source, subscriber) => {\n    let windows = [new Subject()];\n    let starts = [];\n    let count = 0;\n    subscriber.next(windows[0].asObservable());\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      for (const window of windows) {\n        window.next(value);\n      }\n      const c = count - windowSize + 1;\n      if (c >= 0 && c % startEvery === 0) {\n        windows.shift().complete();\n      }\n      if (++count % startEvery === 0) {\n        const window = new Subject();\n        windows.push(window);\n        subscriber.next(window.asObservable());\n      }\n    }, () => {\n      while (windows.length > 0) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, err => {\n      while (windows.length > 0) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    }, () => {\n      starts = null;\n      windows = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "windowCount", "windowSize", "startWindowEvery", "startEvery", "source", "subscriber", "windows", "starts", "count", "next", "asObservable", "subscribe", "value", "window", "c", "shift", "complete", "push", "length", "err", "error"], "sources": ["C:/Users/<USER>/Desktop/Mywebsite/TouradDah/node_modules/rxjs/dist/esm/internal/operators/windowCount.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n    const startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n    return operate((source, subscriber) => {\n        let windows = [new Subject()];\n        let starts = [];\n        let count = 0;\n        subscriber.next(windows[0].asObservable());\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            for (const window of windows) {\n                window.next(value);\n            }\n            const c = count - windowSize + 1;\n            if (c >= 0 && c % startEvery === 0) {\n                windows.shift().complete();\n            }\n            if (++count % startEvery === 0) {\n                const window = new Subject();\n                windows.push(window);\n                subscriber.next(window.asObservable());\n            }\n        }, () => {\n            while (windows.length > 0) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, (err) => {\n            while (windows.length > 0) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        }, () => {\n            starts = null;\n            windows = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAEC,gBAAgB,GAAG,CAAC,EAAE;EAC1D,MAAMC,UAAU,GAAGD,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGD,UAAU;EACvE,OAAOH,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,OAAO,GAAG,CAAC,IAAIT,OAAO,CAAC,CAAC,CAAC;IAC7B,IAAIU,MAAM,GAAG,EAAE;IACf,IAAIC,KAAK,GAAG,CAAC;IACbH,UAAU,CAACI,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACI,YAAY,CAAC,CAAC,CAAC;IAC1CN,MAAM,CAACO,SAAS,CAACZ,wBAAwB,CAACM,UAAU,EAAGO,KAAK,IAAK;MAC7D,KAAK,MAAMC,MAAM,IAAIP,OAAO,EAAE;QAC1BO,MAAM,CAACJ,IAAI,CAACG,KAAK,CAAC;MACtB;MACA,MAAME,CAAC,GAAGN,KAAK,GAAGP,UAAU,GAAG,CAAC;MAChC,IAAIa,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGX,UAAU,KAAK,CAAC,EAAE;QAChCG,OAAO,CAACS,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;MACA,IAAI,EAAER,KAAK,GAAGL,UAAU,KAAK,CAAC,EAAE;QAC5B,MAAMU,MAAM,GAAG,IAAIhB,OAAO,CAAC,CAAC;QAC5BS,OAAO,CAACW,IAAI,CAACJ,MAAM,CAAC;QACpBR,UAAU,CAACI,IAAI,CAACI,MAAM,CAACH,YAAY,CAAC,CAAC,CAAC;MAC1C;IACJ,CAAC,EAAE,MAAM;MACL,OAAOJ,OAAO,CAACY,MAAM,GAAG,CAAC,EAAE;QACvBZ,OAAO,CAACS,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAC9B;MACAX,UAAU,CAACW,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAGG,GAAG,IAAK;MACR,OAAOb,OAAO,CAACY,MAAM,GAAG,CAAC,EAAE;QACvBZ,OAAO,CAACS,KAAK,CAAC,CAAC,CAACK,KAAK,CAACD,GAAG,CAAC;MAC9B;MACAd,UAAU,CAACe,KAAK,CAACD,GAAG,CAAC;IACzB,CAAC,EAAE,MAAM;MACLZ,MAAM,GAAG,IAAI;MACbD,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}