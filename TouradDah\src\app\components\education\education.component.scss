.education-section {
  padding: 100px 0;
  background: var(--bg-primary);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
  }
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--gray);
  max-width: 600px;
  margin: 0 auto;
}

.education-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.block-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;

  i {
    color: var(--primary-color);
    font-size: 1.5rem;
  }
}

// Timeline pour la formation
.timeline {
  position: relative;
  padding: 2rem 0;

  &::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
    transform: translateX(-50%);
  }
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;

  &.left .timeline-content {
    flex-direction: row;
    text-align: right;

    .timeline-card {
      margin-right: 2rem;
    }
  }

  &.right .timeline-content {
    flex-direction: row-reverse;
    text-align: left;

    .timeline-card {
      margin-left: 2rem;
    }
  }
}

.timeline-content {
  display: flex;
  align-items: center;
  position: relative;
}

.timeline-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  position: relative;
  z-index: 2;
  box-shadow: 0 0 0 4px var(--bg-primary);
}

.timeline-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 1.5rem;
  max-width: 300px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
  }
}

.card-header {
  margin-bottom: 0.8rem;
}

.timeline-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.3rem;
}

.timeline-period {
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.timeline-institution {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 0.8rem;
}

.timeline-description {
  color: var(--gray);
  font-size: 0.9rem;
  line-height: 1.5;
}

// Liste d'expérience
.experience-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.experience-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
  }

  &:hover {
    transform: translateX(5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
  }
}

.experience-header {
  margin-bottom: 1rem;
}

.experience-position {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.3rem;
}

.experience-company {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 1.1rem;
  margin-bottom: 0.3rem;
}

.experience-period {
  color: var(--gray);
  font-size: 0.9rem;
  font-style: italic;
}

.experience-description {
  color: var(--gray);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.experience-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-badge {
  background: var(--bg-primary);
  color: var(--primary-color);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid var(--primary-color);
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color);
    color: white;
  }
}

// Responsive
@media (max-width: 768px) {
  .education-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .timeline {
    &::before {
      left: 30px;
    }
  }

  .timeline-item {
    &.left,
    &.right {
      .timeline-content {
        flex-direction: row;
        text-align: left;

        .timeline-card {
          margin-left: 2rem;
          margin-right: 0;
        }
      }
    }
  }

  .timeline-icon {
    position: absolute;
    left: 5px;
  }

  .timeline-card {
    max-width: none;
    margin-left: 4rem !important;
  }

  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .education-section {
    padding: 60px 0;
  }

  .timeline-card,
  .experience-item {
    padding: 1rem;
  }

  .timeline-title,
  .experience-position {
    font-size: 1.1rem;
  }

  .block-title {
    font-size: 1.5rem;
  }

  .timeline-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}
