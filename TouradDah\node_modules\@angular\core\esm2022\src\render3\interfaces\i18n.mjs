/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Marks that the next string is an element name.
 *
 * See `I18nMutateOpCodes` documentation.
 */
export const ELEMENT_MARKER = {
    marker: 'element'
};
/**
 * Marks that the next string is comment text need for ICU.
 *
 * See `I18nMutateOpCodes` documentation.
 */
export const ICU_MARKER = {
    marker: 'ICU'
};
/**
 * See `I18nCreateOpCodes`
 */
export var I18nCreateOpCode;
(function (I18nCreateOpCode) {
    /**
     * Number of bits to shift index so that it can be combined with the `APPEND_EAGERLY` and
     * `COMMENT`.
     */
    I18nCreateOpCode[I18nCreateOpCode["SHIFT"] = 2] = "SHIFT";
    /**
     * Should the node be appended to parent immediately after creation.
     */
    I18nCreateOpCode[I18nCreateOpCode["APPEND_EAGERLY"] = 1] = "APPEND_EAGERLY";
    /**
     * If set the node should be comment (rather than a text) node.
     */
    I18nCreateOpCode[I18nCreateOpCode["COMMENT"] = 2] = "COMMENT";
})(I18nCreateOpCode || (I18nCreateOpCode = {}));
//# sourceMappingURL=data:application/json;base64,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