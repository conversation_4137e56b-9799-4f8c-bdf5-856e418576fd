/angular-portfolio/angular-portfolio/src/app/components/skills/skills.component.css

.skills-content {
    display: flex;
    flex-direction: column;
    margin: 20px 0;
}

.skill-category {
    margin-bottom: 30px;
}

.category-title {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 10px;
}

.row {
    display: flex;
    flex-wrap: wrap;
}

.item {
    flex: 1 1 20%;
    margin: 10px;
    text-align: center;
}

.item img {
    width: 50px;
    height: 50px;
    margin-bottom: 5px;
}

.skill-progress {
    background-color: #f3f3f3;
    border-radius: 5px;
    overflow: hidden;
    height: 10px;
    margin-top: 5px;
}

.progress-bar {
    background-color: #4caf50;
    height: 100%;
    transition: width 0.5s;
}

.percentage {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}