{"ast": null, "code": "export var fromCodePoint = String.fromCodePoint || function (astralCodePoint) {\n  return String.fromCharCode(Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800, (astralCodePoint - 0x10000) % 0x400 + 0xdc00);\n};\n// @ts-expect-error - String.prototype.codePointAt might not exist in older node versions\nexport var getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n};\nexport var highSurrogateFrom = 0xd800;\nexport var highSurrogateTo = 0xdbff;", "map": {"version": 3, "names": ["fromCodePoint", "String", "astralCodePoint", "fromCharCode", "Math", "floor", "getCodePoint", "prototype", "codePointAt", "input", "position", "charCodeAt", "highSurrogateFrom", "highSurrogateTo"], "sources": ["C:/Users/<USER>/Desktop/Mywebsite/TouradDah/node_modules/html-entities/dist/esm/surrogate-pairs.js"], "sourcesContent": ["export var fromCodePoint = String.fromCodePoint ||\n    function (astralCodePoint) {\n        return String.fromCharCode(Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800, ((astralCodePoint - 0x10000) % 0x400) + 0xdc00);\n    };\n// @ts-expect-error - String.prototype.codePointAt might not exist in older node versions\nexport var getCodePoint = String.prototype.codePointAt\n    ? function (input, position) {\n        return input.codePointAt(position);\n    }\n    : function (input, position) {\n        return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n    };\nexport var highSurrogateFrom = 0xd800;\nexport var highSurrogateTo = 0xdbff;\n"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAGC,MAAM,CAACD,aAAa,IAC3C,UAAUE,eAAe,EAAE;EACvB,OAAOD,MAAM,CAACE,YAAY,CAACC,IAAI,CAACC,KAAK,CAAC,CAACH,eAAe,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,MAAM,EAAG,CAACA,eAAe,GAAG,OAAO,IAAI,KAAK,GAAI,MAAM,CAAC;AACxI,CAAC;AACL;AACA,OAAO,IAAII,YAAY,GAAGL,MAAM,CAACM,SAAS,CAACC,WAAW,GAChD,UAAUC,KAAK,EAAEC,QAAQ,EAAE;EACzB,OAAOD,KAAK,CAACD,WAAW,CAACE,QAAQ,CAAC;AACtC,CAAC,GACC,UAAUD,KAAK,EAAEC,QAAQ,EAAE;EACzB,OAAO,CAACD,KAAK,CAACE,UAAU,CAACD,QAAQ,CAAC,GAAG,MAAM,IAAI,KAAK,GAAGD,KAAK,CAACE,UAAU,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;AAC5G,CAAC;AACL,OAAO,IAAIE,iBAAiB,GAAG,MAAM;AACrC,OAAO,IAAIC,eAAe,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}