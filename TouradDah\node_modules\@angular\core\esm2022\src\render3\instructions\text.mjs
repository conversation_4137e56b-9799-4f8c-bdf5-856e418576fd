/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { validateMatchingNode } from '../../hydration/error_handling';
import { locateNextRNode } from '../../hydration/node_lookup_utils';
import { isDisconnectedNode, markRNodeAsClaimedByHydration } from '../../hydration/utils';
import { isDetachedByI18n } from '../../i18n/utils';
import { assertEqual, assertIndexInRange } from '../../util/assert';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { appendChild, createTextNode } from '../node_manipulation';
import { getBindingIndex, getLView, getTView, isInSkipHydrationBlock, lastNode<PERSON>asCreated, setCurrentTNode, wasLastNodeCreated } from '../state';
import { getOrCreateTNode } from './shared';
/**
 * Create static text node
 *
 * @param index Index of the node in the data array
 * @param value Static string value to write.
 *
 * @codeGenApi
 */
export function ɵɵtext(index, value = '') {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = index + HEADER_OFFSET;
    ngDevMode &&
        assertEqual(getBindingIndex(), tView.bindingStartIndex, 'text nodes should be created before any bindings');
    ngDevMode && assertIndexInRange(lView, adjustedIndex);
    const tNode = tView.firstCreatePass ?
        getOrCreateTNode(tView, adjustedIndex, 1 /* TNodeType.Text */, value, null) :
        tView.data[adjustedIndex];
    const textNative = _locateOrCreateTextNode(tView, lView, tNode, value, index);
    lView[adjustedIndex] = textNative;
    if (wasLastNodeCreated()) {
        appendChild(tView, lView, textNative, tNode);
    }
    // Text nodes are self closing.
    setCurrentTNode(tNode, false);
}
let _locateOrCreateTextNode = (tView, lView, tNode, value, index) => {
    lastNodeWasCreated(true);
    return createTextNode(lView[RENDERER], value);
};
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode of text nodes.
 */
function locateOrCreateTextNodeImpl(tView, lView, tNode, value, index) {
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo || isInSkipHydrationBlock() ||
        isDetachedByI18n(tNode) || isDisconnectedNode(hydrationInfo, index);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createTextNode(lView[RENDERER], value);
    }
    // Hydration mode, looking up an existing element in DOM.
    const textNative = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateMatchingNode(textNative, Node.TEXT_NODE, null, lView, tNode);
    ngDevMode && markRNodeAsClaimedByHydration(textNative);
    return textNative;
}
export function enableLocateOrCreateTextNodeImpl() {
    _locateOrCreateTextNode = locateOrCreateTextNodeImpl;
}
//# sourceMappingURL=data:application/json;base64,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