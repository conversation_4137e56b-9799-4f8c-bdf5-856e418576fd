{"name": "validate-npm-package-name", "version": "6.0.1", "description": "Give me a string and I'll tell you if it's a valid npm package name", "main": "lib/", "directories": {"test": "test"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.3", "tap": "^16.0.1"}, "scripts": {"cov:test": "TAP_FLAGS='--cov' npm run test:code", "test:code": "tap ${TAP_FLAGS:-'--'} test/*.js", "test:style": "standard", "test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/validate-npm-package-name.git"}, "keywords": ["npm", "package", "names", "validation"], "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/validate-npm-package-name/issues"}, "homepage": "https://github.com/npm/validate-npm-package-name", "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}