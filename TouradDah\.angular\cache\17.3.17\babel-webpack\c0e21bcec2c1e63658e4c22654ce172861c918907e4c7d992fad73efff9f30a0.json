{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AboutComponent } from '../about/about.component';\nimport { SkillsComponent } from '../skills/skills.component';\nimport { PortfolioComponent } from '../portfolio/portfolio.component';\nimport { EducationComponent } from '../education/education.component';\nimport { ContactComponent } from '../contact/contact.component';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  ngOnInit() {\n    this.initTypewriter();\n  }\n  ngAfterViewInit() {\n    this.initScrollAnimations();\n  }\n  initTypewriter() {\n    const nameElement = document.getElementById('typewriter-name');\n    const titleElement = document.getElementById('typewriter-title');\n    if (nameElement && titleElement) {\n      const name = 'Tourad Med Mahmoud Dah';\n      const title = 'Développeur Full-stack';\n      this.typeWriter(nameElement, name, 100, () => {\n        setTimeout(() => {\n          this.typeWriter(titleElement, title, 80);\n        }, 500);\n      });\n    }\n  }\n  typeWriter(element, text, speed, callback) {\n    let i = 0;\n    element.innerHTML = '';\n    const timer = setInterval(() => {\n      if (i < text.length) {\n        element.innerHTML += text.charAt(i);\n        i++;\n      } else {\n        clearInterval(timer);\n        if (callback) callback();\n      }\n    }, speed);\n  }\n  initScrollAnimations() {\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n    const animatedElements = document.querySelectorAll('.animate-on-scroll');\n    animatedElements.forEach(el => observer.observe(el));\n  }\n  scrollToPortfolio() {\n    const portfolioSection = document.getElementById('portfolio');\n    if (portfolioSection) {\n      const offsetTop = portfolioSection.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 52,\n      vars: 0,\n      consts: [[\"id\", \"home\", 1, \"hero-section\"], [1, \"container\"], [1, \"hero-content\"], [1, \"content-text\"], [1, \"greeting-section\"], [1, \"greeting\"], [1, \"text-primary\"], [\"id\", \"typewriter-name\", 1, \"hero-name\"], [\"id\", \"typewriter-title\", 1, \"title\"], [1, \"subtitle\"], [1, \"skills-highlight\"], [1, \"skill-badge\"], [1, \"fas\", \"fa-code\"], [1, \"fas\", \"fa-bug\"], [1, \"social-links\"], [\"href\", \"https://github.com/tourad\", \"target\", \"_blank\", \"rel\", \"noopener\", \"title\", \"GitHub\"], [1, \"fa-brands\", \"fa-github\"], [\"href\", \"#\", \"title\", \"Facebook\"], [1, \"fa-brands\", \"fa-square-facebook\"], [\"href\", \"#\", \"target\", \"_blank\", \"rel\", \"noopener\", \"title\", \"Twitter\"], [1, \"fa-brands\", \"fa-square-x-twitter\"], [\"href\", \"#\", \"title\", \"LinkedIn\"], [1, \"fa-brands\", \"fa-linkedin\"], [\"href\", \"http://wa.me/+22238493149\", \"target\", \"_blank\", \"rel\", \"noopener\", \"title\", \"WhatsApp\"], [1, \"fa-brands\", \"fa-whatsapp\"], [1, \"cta-buttons\"], [1, \"btn-primary\", \"enhanced\", 3, \"click\"], [1, \"btn-text\"], [1, \"btn-icon\"], [1, \"fa-solid\", \"fa-rocket\"], [1, \"btn-shine\"], [1, \"hero-image\"], [1, \"image-container\"], [\"src\", \"assets/images/Tourad1.jpg\", \"alt\", \"Tourad Med Mahmoud Dah - D\\u00E9veloppeur Full-stack\"], [1, \"image-overlay\"], [1, \"floating-badge\"], [\"id\", \"about\"], [\"id\", \"skills\"], [\"id\", \"portfolio\"], [\"id\", \"education\"], [\"id\", \"contact\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"p\", 5)(6, \"span\", 6);\n          i0.ɵɵtext(7, \"Salut, je suis\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(8, \"h1\", 7)(9, \"p\", 8);\n          i0.ɵɵelementStart(10, \"p\", 9);\n          i0.ɵɵtext(11, \"Sp\\u00E9cialis\\u00E9 en cybers\\u00E9curit\\u00E9 et d\\u00E9veloppement web moderne\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11);\n          i0.ɵɵelement(14, \"i\", 12);\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Full-stack\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 11);\n          i0.ɵɵelement(18, \"i\", 13);\n          i0.ɵɵelementStart(19, \"span\");\n          i0.ɵɵtext(20, \"Bug Bounty\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"a\", 15);\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"a\", 17);\n          i0.ɵɵelement(25, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"a\", 19);\n          i0.ɵɵelement(27, \"i\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"a\", 21);\n          i0.ɵɵelement(29, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"a\", 23);\n          i0.ɵɵelement(31, \"i\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 25)(33, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_33_listener() {\n            return ctx.scrollToPortfolio();\n          });\n          i0.ɵɵelementStart(34, \"span\", 27);\n          i0.ɵɵtext(35, \"D\\u00E9couvrir mes projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"span\", 28);\n          i0.ɵɵelement(37, \"i\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"div\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 31)(40, \"div\", 32);\n          i0.ɵɵelement(41, \"img\", 33);\n          i0.ɵɵelementStart(42, \"div\", 34)(43, \"div\", 35);\n          i0.ɵɵelement(44, \"i\", 12);\n          i0.ɵɵelementStart(45, \"span\");\n          i0.ɵɵtext(46, \"D\\u00E9veloppeur\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelement(47, \"app-about\", 36)(48, \"app-skills\", 37)(49, \"app-portfolio\", 38)(50, \"app-education\", 39)(51, \"app-contact\", 40);\n        }\n      },\n      dependencies: [CommonModule, AboutComponent, SkillsComponent, PortfolioComponent, EducationComponent, ContactComponent],\n      styles: [\".hero-section[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-primary) 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"1\\\" fill=\\\"%23ffffff\\\" opacity=\\\"0.05\\\"/><circle cx=\\\"75\\\" cy=\\\"75\\\" r=\\\"1\\\" fill=\\\"%23ffffff\\\" opacity=\\\"0.05\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  pointer-events: none;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 4rem;\\n  align-items: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.content-text[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInLeft 1s ease-out;\\n}\\n\\n.greeting-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.greeting[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-bottom: 1rem;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out 0.2s forwards;\\n}\\n\\n.hero-name[_ngcontent-%COMP%] {\\n  font-size: 3.5rem;\\n  font-weight: 700;\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n  line-height: 1.2;\\n  border-right: 3px solid var(--primary-color);\\n  padding-right: 10px;\\n  animation: _ngcontent-%COMP%_blink 1s infinite;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n  border-right: 3px solid var(--primary-color);\\n  padding-right: 10px;\\n  animation: _ngcontent-%COMP%_blink 1s infinite;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: var(--gray);\\n  margin-bottom: 2rem;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out 0.6s forwards;\\n}\\n\\n.skills-highlight[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin: 2rem 0;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out 0.8s forwards;\\n}\\n\\n.skill-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  background: var(--card-bg);\\n  padding: 0.8rem 1.2rem;\\n  border-radius: 25px;\\n  border: 1px solid var(--border-color);\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n.skill-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(32, 201, 151, 0.2);\\n  border-color: var(--primary-color);\\n}\\n.skill-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 18px;\\n}\\n.skill-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n\\n.social-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin: 2rem 0;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out 1s forwards;\\n}\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--text-color);\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n  transform: translateY(-5px) scale(1.1);\\n  box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);\\n}\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n\\n.cta-buttons[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s ease-out 1.2s forwards;\\n}\\n\\n.btn-primary.enhanced[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: var(--gradient-primary);\\n  color: white;\\n  border: none;\\n  padding: 1rem 2rem;\\n  border-radius: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);\\n}\\n.btn-primary.enhanced[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 12px 35px rgba(32, 201, 151, 0.4);\\n}\\n.btn-primary.enhanced[_ngcontent-%COMP%]   .btn-shine[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.btn-primary.enhanced[_ngcontent-%COMP%]:hover   .btn-shine[_ngcontent-%COMP%] {\\n  left: 100%;\\n}\\n\\n.hero-image[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeInRight 1s ease-out;\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 400px;\\n  width: 100%;\\n}\\n\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  border-radius: 20px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);\\n}\\n\\n.image-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 20px;\\n  background: linear-gradient(45deg, rgba(3, 59, 74, 0.1), rgba(32, 201, 151, 0.1));\\n}\\n\\n.floating-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  background: var(--primary-color);\\n  color: white;\\n  padding: 0.5rem 1rem;\\n  border-radius: 25px;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 14px;\\n  font-weight: 600;\\n  animation: _ngcontent-%COMP%_float 3s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInLeft {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-50px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(50px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_blink {\\n  0%, 50% {\\n    border-color: var(--primary-color);\\n  }\\n  51%, 100% {\\n    border-color: transparent;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .hero-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 2rem;\\n    text-align: center;\\n  }\\n  .hero-name[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .skills-highlight[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    flex-wrap: wrap;\\n  }\\n  .social-links[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .hero-name[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n  .skills-highlight[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .skill-badge[_ngcontent-%COMP%] {\\n    padding: 0.6rem 1rem;\\n    font-size: 14px;\\n  }\\n  .social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLCtFQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUNGO0FBQ0U7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsK1hBQUE7RUFDQSxvQkFBQTtBQUNKOztBQUdBO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBQUY7O0FBR0E7RUFDRSxpQ0FBQTtBQUFGOztBQUdBO0VBQ0UsbUJBQUE7QUFBRjs7QUFHQTtFQUNFLGVBQUE7RUFDQSxtQkFBQTtFQUNBLFVBQUE7RUFDQSwrQ0FBQTtBQUFGOztBQUdBO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLHdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDRDQUFBO0VBQ0EsbUJBQUE7RUFDQSw0QkFBQTtBQUFGOztBQUdBO0VBQ0UsaUJBQUE7RUFDQSwyQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSw0Q0FBQTtFQUNBLG1CQUFBO0VBQ0EsNEJBQUE7QUFBRjs7QUFHQTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLFVBQUE7RUFDQSwrQ0FBQTtBQUFGOztBQUdBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxjQUFBO0VBQ0EsVUFBQTtFQUNBLCtDQUFBO0FBQUY7O0FBR0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsMEJBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EscUNBQUE7RUFDQSx5QkFBQTtFQUNBLHlDQUFBO0FBQUY7QUFFRTtFQUNFLDJCQUFBO0VBQ0EsOENBQUE7RUFDQSxrQ0FBQTtBQUFKO0FBR0U7RUFDRSwyQkFBQTtFQUNBLGVBQUE7QUFESjtBQUlFO0VBQ0UsZ0JBQUE7RUFDQSx3QkFBQTtBQUZKOztBQU1BO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxjQUFBO0VBQ0EsVUFBQTtFQUNBLDZDQUFBO0FBSEY7QUFLRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSwwQkFBQTtFQUNBLHFDQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx3QkFBQTtFQUNBLHFCQUFBO0VBQ0EseUJBQUE7RUFDQSx5Q0FBQTtBQUhKO0FBS0k7RUFDRSxnQ0FBQTtFQUNBLFlBQUE7RUFDQSxzQ0FBQTtFQUNBLDhDQUFBO0FBSE47QUFNSTtFQUNFLGVBQUE7QUFKTjs7QUFTQTtFQUNFLFVBQUE7RUFDQSwrQ0FBQTtBQU5GOztBQVNBO0VBQ0Usa0JBQUE7RUFDQSxtQ0FBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0Esb0JBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSw4Q0FBQTtBQU5GO0FBUUU7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBTko7QUFTRTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHNGQUFBO0VBQ0EscUJBQUE7QUFQSjtBQVVFO0VBQ0UsVUFBQTtBQVJKOztBQVlBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0Esa0NBQUE7QUFURjs7QUFZQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0FBVEY7O0FBWUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsMENBQUE7RUFDQSx5QkFBQTtBQVRGO0FBV0U7RUFDRSxzQkFBQTtFQUNBLDJDQUFBO0FBVEo7O0FBYUE7RUFDRSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLGlGQUFBO0FBVkY7O0FBYUE7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsZ0NBQUE7RUFDQSxZQUFBO0VBQ0Esb0JBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3Q0FBQTtBQVZGOztBQWNBO0VBQ0U7SUFDRSxVQUFBO0lBQ0EsMkJBQUE7RUFYRjtFQWFBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VBWEY7QUFDRjtBQWNBO0VBQ0U7SUFDRSxVQUFBO0lBQ0EsNEJBQUE7RUFaRjtFQWNBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VBWkY7QUFDRjtBQWVBO0VBQ0U7SUFDRSxVQUFBO0lBQ0EsMkJBQUE7RUFiRjtFQWVBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VBYkY7QUFDRjtBQWdCQTtFQUNFO0lBQVUsa0NBQUE7RUFiVjtFQWNBO0lBQVkseUJBQUE7RUFYWjtBQUNGO0FBYUE7RUFDRTtJQUFXLDBCQUFBO0VBVlg7RUFXQTtJQUFNLDRCQUFBO0VBUk47QUFDRjtBQVdBO0VBQ0U7SUFDRSwwQkFBQTtJQUNBLFNBQUE7SUFDQSxrQkFBQTtFQVRGO0VBWUE7SUFDRSxpQkFBQTtFQVZGO0VBYUE7SUFDRSxpQkFBQTtFQVhGO0VBY0E7SUFDRSxlQUFBO0VBWkY7RUFlQTtJQUNFLHVCQUFBO0lBQ0EsZUFBQTtFQWJGO0VBZ0JBO0lBQ0UsdUJBQUE7RUFkRjtBQUNGO0FBaUJBO0VBQ0U7SUFDRSxlQUFBO0VBZkY7RUFrQkE7SUFDRSxpQkFBQTtFQWhCRjtFQW1CQTtJQUNFLFdBQUE7RUFqQkY7RUFvQkE7SUFDRSxvQkFBQTtJQUNBLGVBQUE7RUFsQkY7RUFxQkE7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQW5CRjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmhlcm8tc2VjdGlvbiB7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS1iZy1jb2xvcikgMCUsIHZhcigtLWJnLXByaW1hcnkpIDEwMCUpO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgJjo6YmVmb3JlIHtcbiAgICBjb250ZW50OiAnJztcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgdG9wOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgYm90dG9tOiAwO1xuICAgIGJhY2tncm91bmQ6IHVybCgnZGF0YTppbWFnZS9zdmcreG1sLDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIHZpZXdCb3g9XCIwIDAgMTAwIDEwMFwiPjxkZWZzPjxwYXR0ZXJuIGlkPVwiZ3JhaW5cIiB3aWR0aD1cIjEwMFwiIGhlaWdodD1cIjEwMFwiIHBhdHRlcm5Vbml0cz1cInVzZXJTcGFjZU9uVXNlXCI+PGNpcmNsZSBjeD1cIjI1XCIgY3k9XCIyNVwiIHI9XCIxXCIgZmlsbD1cIiUyM2ZmZmZmZlwiIG9wYWNpdHk9XCIwLjA1XCIvPjxjaXJjbGUgY3g9XCI3NVwiIGN5PVwiNzVcIiByPVwiMVwiIGZpbGw9XCIlMjNmZmZmZmZcIiBvcGFjaXR5PVwiMC4wNVwiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPVwiMTAwXCIgaGVpZ2h0PVwiMTAwXCIgZmlsbD1cInVybCglMjNncmFpbilcIi8+PC9zdmc+Jyk7XG4gICAgcG9pbnRlci1ldmVudHM6IG5vbmU7XG4gIH1cbn1cblxuLmhlcm8tY29udGVudCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDFmcjtcbiAgZ2FwOiA0cmVtO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHotaW5kZXg6IDE7XG59XG5cbi5jb250ZW50LXRleHQge1xuICBhbmltYXRpb246IGZhZGVJbkxlZnQgMXMgZWFzZS1vdXQ7XG59XG5cbi5ncmVldGluZy1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbn1cblxuLmdyZWV0aW5nIHtcbiAgZm9udC1zaXplOiAxOHB4O1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBvcGFjaXR5OiAwO1xuICBhbmltYXRpb246IGZhZGVJblVwIDAuOHMgZWFzZS1vdXQgMC4ycyBmb3J3YXJkcztcbn1cblxuLmhlcm8tbmFtZSB7XG4gIGZvbnQtc2l6ZTogMy41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvcik7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjI7XG4gIGJvcmRlci1yaWdodDogM3B4IHNvbGlkIHZhcigtLXByaW1hcnktY29sb3IpO1xuICBwYWRkaW5nLXJpZ2h0OiAxMHB4O1xuICBhbmltYXRpb246IGJsaW5rIDFzIGluZmluaXRlO1xufVxuXG4udGl0bGUge1xuICBmb250LXNpemU6IDEuOHJlbTtcbiAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICBmb250LXdlaWdodDogNjAwO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBib3JkZXItcmlnaHQ6IDNweCBzb2xpZCB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgcGFkZGluZy1yaWdodDogMTBweDtcbiAgYW5pbWF0aW9uOiBibGluayAxcyBpbmZpbml0ZTtcbn1cblxuLnN1YnRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjJyZW07XG4gIGNvbG9yOiB2YXIoLS1ncmF5KTtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgb3BhY2l0eTogMDtcbiAgYW5pbWF0aW9uOiBmYWRlSW5VcCAwLjhzIGVhc2Utb3V0IDAuNnMgZm9yd2FyZHM7XG59XG5cbi5za2lsbHMtaGlnaGxpZ2h0IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxcmVtO1xuICBtYXJnaW46IDJyZW0gMDtcbiAgb3BhY2l0eTogMDtcbiAgYW5pbWF0aW9uOiBmYWRlSW5VcCAwLjhzIGVhc2Utb3V0IDAuOHMgZm9yd2FyZHM7XG59XG5cbi5za2lsbC1iYWRnZSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkLWJnKTtcbiAgcGFkZGluZzogMC44cmVtIDEuMnJlbTtcbiAgYm9yZGVyLXJhZGl1czogMjVweDtcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYm9yZGVyLWNvbG9yKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG5cbiAgJjpob3ZlciB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xuICAgIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgzMiwgMjAxLCAxNTEsIDAuMik7XG4gICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgfVxuXG4gIGkge1xuICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICBmb250LXNpemU6IDE4cHg7XG4gIH1cblxuICBzcGFuIHtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcbiAgfVxufVxuXG4uc29jaWFsLWxpbmtzIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxcmVtO1xuICBtYXJnaW46IDJyZW0gMDtcbiAgb3BhY2l0eTogMDtcbiAgYW5pbWF0aW9uOiBmYWRlSW5VcCAwLjhzIGVhc2Utb3V0IDFzIGZvcndhcmRzO1xuXG4gIGEge1xuICAgIHdpZHRoOiA1MHB4O1xuICAgIGhlaWdodDogNTBweDtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tY2FyZC1iZyk7XG4gICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYm9yZGVyLWNvbG9yKTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgY29sb3I6IHZhcigtLXRleHQtY29sb3IpO1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KSBzY2FsZSgxLjEpO1xuICAgICAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDMyLCAyMDEsIDE1MSwgMC4zKTtcbiAgICB9XG5cbiAgICBpIHtcbiAgICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICB9XG4gIH1cbn1cblxuLmN0YS1idXR0b25zIHtcbiAgb3BhY2l0eTogMDtcbiAgYW5pbWF0aW9uOiBmYWRlSW5VcCAwLjhzIGVhc2Utb3V0IDEuMnMgZm9yd2FyZHM7XG59XG5cbi5idG4tcHJpbWFyeS5lbmhhbmNlZCB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgYmFja2dyb3VuZDogdmFyKC0tZ3JhZGllbnQtcHJpbWFyeSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgYm9yZGVyOiBub25lO1xuICBwYWRkaW5nOiAxcmVtIDJyZW07XG4gIGJvcmRlci1yYWRpdXM6IDUwcHg7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG4gIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgzMiwgMjAxLCAxNTEsIDAuMyk7XG5cbiAgJjpob3ZlciB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpO1xuICAgIGJveC1zaGFkb3c6IDAgMTJweCAzNXB4IHJnYmEoMzIsIDIwMSwgMTUxLCAwLjQpO1xuICB9XG5cbiAgLmJ0bi1zaGluZSB7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIHRvcDogMDtcbiAgICBsZWZ0OiAtMTAwJTtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDEwMCU7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpLCB0cmFuc3BhcmVudCk7XG4gICAgdHJhbnNpdGlvbjogbGVmdCAwLjVzO1xuICB9XG5cbiAgJjpob3ZlciAuYnRuLXNoaW5lIHtcbiAgICBsZWZ0OiAxMDAlO1xuICB9XG59XG5cbi5oZXJvLWltYWdlIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFuaW1hdGlvbjogZmFkZUluUmlnaHQgMXMgZWFzZS1vdXQ7XG59XG5cbi5pbWFnZS1jb250YWluZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG1heC13aWR0aDogNDAwcHg7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uaW1hZ2UtY29udGFpbmVyIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IGF1dG87XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIGJveC1zaGFkb3c6IDAgMjBweCA0MHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAmOmhvdmVyIHtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xuICAgIGJveC1zaGFkb3c6IDAgMjVweCA1MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG4gIH1cbn1cblxuLmltYWdlLW92ZXJsYXkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJvdHRvbTogMDtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCByZ2JhKDMsIDU5LCA3NCwgMC4xKSwgcmdiYSgzMiwgMjAxLCAxNTEsIDAuMSkpO1xufVxuXG4uZmxvYXRpbmctYmFkZ2Uge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMjBweDtcbiAgcmlnaHQ6IDIwcHg7XG4gIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICBib3JkZXItcmFkaXVzOiAyNXB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDAuNXJlbTtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICBhbmltYXRpb246IGZsb2F0IDNzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xufVxuXG4vLyBBbmltYXRpb25zXG5Aa2V5ZnJhbWVzIGZhZGVJblVwIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMzBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgZmFkZUluTGVmdCB7XG4gIGZyb20ge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MHB4KTtcbiAgfVxuICB0byB7XG4gICAgb3BhY2l0eTogMTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XG4gIH1cbn1cblxuQGtleWZyYW1lcyBmYWRlSW5SaWdodCB7XG4gIGZyb20ge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDUwcHgpO1xuICB9XG4gIHRvIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcbiAgfVxufVxuXG5Aa2V5ZnJhbWVzIGJsaW5rIHtcbiAgMCUsIDUwJSB7IGJvcmRlci1jb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7IH1cbiAgNTElLCAxMDAlIHsgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDsgfVxufVxuXG5Aa2V5ZnJhbWVzIGZsb2F0IHtcbiAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMHB4KTsgfVxuICA1MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpOyB9XG59XG5cbi8vIFJlc3BvbnNpdmVcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuaGVyby1jb250ZW50IHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICBnYXA6IDJyZW07XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG5cbiAgLmhlcm8tbmFtZSB7XG4gICAgZm9udC1zaXplOiAyLjVyZW07XG4gIH1cblxuICAudGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICB9XG5cbiAgLnN1YnRpdGxlIHtcbiAgICBmb250LXNpemU6IDFyZW07XG4gIH1cblxuICAuc2tpbGxzLWhpZ2hsaWdodCB7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgZmxleC13cmFwOiB3cmFwO1xuICB9XG5cbiAgLnNvY2lhbC1saW5rcyB7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gIC5oZXJvLW5hbWUge1xuICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgfVxuXG4gIC50aXRsZSB7XG4gICAgZm9udC1zaXplOiAxLjNyZW07XG4gIH1cblxuICAuc2tpbGxzLWhpZ2hsaWdodCB7XG4gICAgZ2FwOiAwLjVyZW07XG4gIH1cblxuICAuc2tpbGwtYmFkZ2Uge1xuICAgIHBhZGRpbmc6IDAuNnJlbSAxcmVtO1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgfVxuXG4gIC5zb2NpYWwtbGlua3MgYSB7XG4gICAgd2lkdGg6IDQ1cHg7XG4gICAgaGVpZ2h0OiA0NXB4O1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "AboutComponent", "SkillsComponent", "PortfolioComponent", "EducationComponent", "ContactComponent", "HomeComponent", "ngOnInit", "initTypewriter", "ngAfterViewInit", "initScrollAnimations", "nameElement", "document", "getElementById", "titleElement", "name", "title", "typeWriter", "setTimeout", "element", "text", "speed", "callback", "i", "innerHTML", "timer", "setInterval", "length", "char<PERSON>t", "clearInterval", "observerOptions", "threshold", "rootMargin", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "classList", "add", "animatedElements", "querySelectorAll", "el", "observe", "scrollToPortfolio", "portfolioSection", "offsetTop", "window", "scrollTo", "top", "behavior", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HomeComponent_Template_button_click_33_listener", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { AboutComponent } from '../about/about.component';\nimport { SkillsComponent } from '../skills/skills.component';\nimport { PortfolioComponent } from '../portfolio/portfolio.component';\nimport { EducationComponent } from '../education/education.component';\nimport { ContactComponent } from '../contact/contact.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [\n    CommonModule,\n    AboutComponent,\n    SkillsComponent,\n    PortfolioComponent,\n    EducationComponent,\n    ContactComponent\n  ],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit, AfterViewInit {\n  \n  ngOnInit() {\n    this.initTypewriter();\n  }\n\n  ngAfterViewInit() {\n    this.initScrollAnimations();\n  }\n\n  private initTypewriter() {\n    const nameElement = document.getElementById('typewriter-name');\n    const titleElement = document.getElementById('typewriter-title');\n    \n    if (nameElement && titleElement) {\n      const name = 'Tourad Med Mahmoud Dah';\n      const title = 'Développeur Full-stack';\n      \n      this.typeWriter(nameElement, name, 100, () => {\n        setTimeout(() => {\n          this.typeWriter(titleElement, title, 80);\n        }, 500);\n      });\n    }\n  }\n\n  private typeWriter(element: HTMLElement, text: string, speed: number, callback?: () => void) {\n    let i = 0;\n    element.innerHTML = '';\n    \n    const timer = setInterval(() => {\n      if (i < text.length) {\n        element.innerHTML += text.charAt(i);\n        i++;\n      } else {\n        clearInterval(timer);\n        if (callback) callback();\n      }\n    }, speed);\n  }\n\n  private initScrollAnimations() {\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -50px 0px'\n    };\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    const animatedElements = document.querySelectorAll('.animate-on-scroll');\n    animatedElements.forEach(el => observer.observe(el));\n  }\n\n  scrollToPortfolio() {\n    const portfolioSection = document.getElementById('portfolio');\n    if (portfolioSection) {\n      const offsetTop = portfolioSection.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n  }\n}\n", "<!-- Section Hero -->\n<section id=\"home\" class=\"hero-section\">\n  <div class=\"container\">\n    <div class=\"hero-content\">\n      <div class=\"content-text\">\n        <div class=\"greeting-section\">\n          <p class=\"greeting\"><span class=\"text-primary\">Sal<PERSON>, je suis</span></p>\n          <h1 id=\"typewriter-name\" class=\"hero-name\"></h1>\n          <p class=\"title\" id=\"typewriter-title\"></p>\n          <p class=\"subtitle\">Spécialisé en cybersécurité et développement web moderne</p>\n        </div>\n\n        <div class=\"skills-highlight\">\n          <div class=\"skill-badge\">\n            <i class=\"fas fa-code\"></i>\n            <span>Full-stack</span>\n          </div>\n          <div class=\"skill-badge\">\n            <i class=\"fas fa-bug\"></i>\n            <span>Bug Bounty</span>\n          </div>\n        </div>\n\n        <div class=\"social-links\">\n          <a href=\"https://github.com/tourad\" target=\"_blank\" rel=\"noopener\" title=\"GitHub\">\n            <i class=\"fa-brands fa-github\"></i>\n          </a>\n          <a href=\"#\" title=\"Facebook\">\n            <i class=\"fa-brands fa-square-facebook\"></i>\n          </a>\n          <a href=\"#\" target=\"_blank\" rel=\"noopener\" title=\"Twitter\">\n            <i class=\"fa-brands fa-square-x-twitter\"></i>\n          </a>\n          <a href=\"#\" title=\"LinkedIn\">\n            <i class=\"fa-brands fa-linkedin\"></i>\n          </a>\n          <a href=\"http://wa.me/+22238493149\" target=\"_blank\" rel=\"noopener\" title=\"WhatsApp\">\n            <i class=\"fa-brands fa-whatsapp\"></i>\n          </a>\n        </div>\n\n        <div class=\"cta-buttons\">\n          <button class=\"btn-primary enhanced\" (click)=\"scrollToPortfolio()\">\n            <span class=\"btn-text\">Découvrir mes projets</span>\n            <span class=\"btn-icon\">\n              <i class=\"fa-solid fa-rocket\"></i>\n            </span>\n            <div class=\"btn-shine\"></div>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"hero-image\">\n        <div class=\"image-container\">\n          <img src=\"assets/images/Tourad1.jpg\" alt=\"Tourad Med Mahmoud Dah - Développeur Full-stack\">\n          <div class=\"image-overlay\">\n            <div class=\"floating-badge\">\n              <i class=\"fas fa-code\"></i>\n              <span>Développeur</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n\n<!-- Sections du portfolio -->\n<app-about id=\"about\"></app-about>\n<app-skills id=\"skills\"></app-skills>\n<app-portfolio id=\"portfolio\"></app-portfolio>\n<app-education id=\"education\"></app-education>\n<app-contact id=\"contact\"></app-contact>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,gBAAgB,QAAQ,8BAA8B;;AAgB/D,OAAM,MAAOC,aAAa;EAExBC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEQF,cAAcA,CAAA;IACpB,MAAMG,WAAW,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;IAC9D,MAAMC,YAAY,GAAGF,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAEhE,IAAIF,WAAW,IAAIG,YAAY,EAAE;MAC/B,MAAMC,IAAI,GAAG,wBAAwB;MACrC,MAAMC,KAAK,GAAG,wBAAwB;MAEtC,IAAI,CAACC,UAAU,CAACN,WAAW,EAAEI,IAAI,EAAE,GAAG,EAAE,MAAK;QAC3CG,UAAU,CAAC,MAAK;UACd,IAAI,CAACD,UAAU,CAACH,YAAY,EAAEE,KAAK,EAAE,EAAE,CAAC;QAC1C,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;;EAEN;EAEQC,UAAUA,CAACE,OAAoB,EAAEC,IAAY,EAAEC,KAAa,EAAEC,QAAqB;IACzF,IAAIC,CAAC,GAAG,CAAC;IACTJ,OAAO,CAACK,SAAS,GAAG,EAAE;IAEtB,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAK;MAC7B,IAAIH,CAAC,GAAGH,IAAI,CAACO,MAAM,EAAE;QACnBR,OAAO,CAACK,SAAS,IAAIJ,IAAI,CAACQ,MAAM,CAACL,CAAC,CAAC;QACnCA,CAAC,EAAE;OACJ,MAAM;QACLM,aAAa,CAACJ,KAAK,CAAC;QACpB,IAAIH,QAAQ,EAAEA,QAAQ,EAAE;;IAE5B,CAAC,EAAED,KAAK,CAAC;EACX;EAEQX,oBAAoBA,CAAA;IAC1B,MAAMoB,eAAe,GAAG;MACtBC,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;KACb;IAED,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAI;MACpDA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;QACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;;MAEzC,CAAC,CAAC;IACJ,CAAC,EAAEX,eAAe,CAAC;IAEnB,MAAMY,gBAAgB,GAAG9B,QAAQ,CAAC+B,gBAAgB,CAAC,oBAAoB,CAAC;IACxED,gBAAgB,CAACN,OAAO,CAACQ,EAAE,IAAIX,QAAQ,CAACY,OAAO,CAACD,EAAE,CAAC,CAAC;EACtD;EAEAE,iBAAiBA,CAAA;IACf,MAAMC,gBAAgB,GAAGnC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;IAC7D,IAAIkC,gBAAgB,EAAE;MACpB,MAAMC,SAAS,GAAGD,gBAAgB,CAACC,SAAS,GAAG,EAAE;MACjDC,MAAM,CAACC,QAAQ,CAAC;QACdC,GAAG,EAAEH,SAAS;QACdI,QAAQ,EAAE;OACX,CAAC;;EAEN;;;uBApEW9C,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAA+C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBIP,EAL9B,CAAAS,cAAA,iBAAwC,aACf,aACK,aACE,aACM,WACR,cAA2B;UAAAT,EAAA,CAAAU,MAAA,qBAAc;UAAOV,EAAP,CAAAW,YAAA,EAAO,EAAI;UAExEX,EADA,CAAAY,SAAA,YAAgD,WACL;UAC3CZ,EAAA,CAAAS,cAAA,YAAoB;UAAAT,EAAA,CAAAU,MAAA,yFAAwD;UAC9EV,EAD8E,CAAAW,YAAA,EAAI,EAC5E;UAGJX,EADF,CAAAS,cAAA,eAA8B,eACH;UACvBT,EAAA,CAAAY,SAAA,aAA2B;UAC3BZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,kBAAU;UAClBV,EADkB,CAAAW,YAAA,EAAO,EACnB;UACNX,EAAA,CAAAS,cAAA,eAAyB;UACvBT,EAAA,CAAAY,SAAA,aAA0B;UAC1BZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,kBAAU;UAEpBV,EAFoB,CAAAW,YAAA,EAAO,EACnB,EACF;UAGJX,EADF,CAAAS,cAAA,eAA0B,aAC0D;UAChFT,EAAA,CAAAY,SAAA,aAAmC;UACrCZ,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,aAA6B;UAC3BT,EAAA,CAAAY,SAAA,aAA4C;UAC9CZ,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,aAA2D;UACzDT,EAAA,CAAAY,SAAA,aAA6C;UAC/CZ,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,aAA6B;UAC3BT,EAAA,CAAAY,SAAA,aAAqC;UACvCZ,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,aAAoF;UAClFT,EAAA,CAAAY,SAAA,aAAqC;UAEzCZ,EADE,CAAAW,YAAA,EAAI,EACA;UAGJX,EADF,CAAAS,cAAA,eAAyB,kBAC4C;UAA9BT,EAAA,CAAAa,UAAA,mBAAAC,gDAAA;YAAA,OAASN,GAAA,CAAAlB,iBAAA,EAAmB;UAAA,EAAC;UAChEU,EAAA,CAAAS,cAAA,gBAAuB;UAAAT,EAAA,CAAAU,MAAA,kCAAqB;UAAAV,EAAA,CAAAW,YAAA,EAAO;UACnDX,EAAA,CAAAS,cAAA,gBAAuB;UACrBT,EAAA,CAAAY,SAAA,aAAkC;UACpCZ,EAAA,CAAAW,YAAA,EAAO;UACPX,EAAA,CAAAY,SAAA,eAA6B;UAGnCZ,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;UAGJX,EADF,CAAAS,cAAA,eAAwB,eACO;UAC3BT,EAAA,CAAAY,SAAA,eAA2F;UAEzFZ,EADF,CAAAS,cAAA,eAA2B,eACG;UAC1BT,EAAA,CAAAY,SAAA,aAA2B;UAC3BZ,EAAA,CAAAS,cAAA,YAAM;UAAAT,EAAA,CAAAU,MAAA,wBAAW;UAO/BV,EAP+B,CAAAW,YAAA,EAAO,EACpB,EACF,EACF,EACF,EACF,EACF,EACE;UAOVX,EAJA,CAAAY,SAAA,qBAAkC,sBACG,yBACS,yBACA,uBACN;;;qBD5DpCpE,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB;MAAAkE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}