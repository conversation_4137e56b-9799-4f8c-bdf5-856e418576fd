{"ast": null, "code": "import { provideRouter } from '@angular/router';\nimport { routes } from './app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideAnimations()]\n};", "map": {"version": 3, "names": ["provideRouter", "routes", "provideAnimations", "appConfig", "providers"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\n\nexport const appConfig: ApplicationConfig = {\n  providers: [\n    provideRouter(routes),\n    provideAnimations()\n  ]\n};\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,iBAAiB,QAAQ,sCAAsC;AAExE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTJ,aAAa,CAACC,MAAM,CAAC,EACrBC,iBAAiB,EAAE;CAEtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}