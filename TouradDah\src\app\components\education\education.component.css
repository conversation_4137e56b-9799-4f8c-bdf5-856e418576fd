.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #333;
}

.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    margin: 20px 0;
    position: relative;
}

.timeline-dot {
    position: absolute;
    left: -15px;
    top: 0;
    width: 30px;
    height: 30px;
    background-color: #007bff;
    border-radius: 50%;
}

.timeline-content {
    padding-left: 50px;
}

.education-card {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    margin-top: 10px;
}

.card-badge {
    background-color: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
}

.education-details {
    margin-top: 10px;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.detail-item i {
    margin-right: 5px;
    color: #007bff;
}