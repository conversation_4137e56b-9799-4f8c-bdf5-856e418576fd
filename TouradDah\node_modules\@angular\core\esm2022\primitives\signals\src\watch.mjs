/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, isInNotificationPhase, REACTIVE_NODE, SIGNAL } from './graph';
export function createWatch(fn, schedule, allowSignalWrites) {
    const node = Object.create(WATCH_NODE);
    if (allowSignalWrites) {
        node.consumerAllowSignalWrites = true;
    }
    node.fn = fn;
    node.schedule = schedule;
    const registerOnCleanup = (cleanupFn) => {
        node.cleanupFn = cleanupFn;
    };
    function isWatchNodeDestroyed(node) {
        return node.fn === null && node.schedule === null;
    }
    function destroyWatchNode(node) {
        if (!isWatchNodeDestroyed(node)) {
            consumerDestroy(node); // disconnect watcher from the reactive graph
            node.cleanupFn();
            // nullify references to the integration functions to mark node as destroyed
            node.fn = null;
            node.schedule = null;
            node.cleanupFn = NOOP_CLEANUP_FN;
        }
    }
    const run = () => {
        if (node.fn === null) {
            // trying to run a destroyed watch is noop
            return;
        }
        if (isInNotificationPhase()) {
            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);
        }
        node.dirty = false;
        if (node.hasRun && !consumerPollProducersForChange(node)) {
            return;
        }
        node.hasRun = true;
        const prevConsumer = consumerBeforeComputation(node);
        try {
            node.cleanupFn();
            node.cleanupFn = NOOP_CLEANUP_FN;
            node.fn(registerOnCleanup);
        }
        finally {
            consumerAfterComputation(node, prevConsumer);
        }
    };
    node.ref = {
        notify: () => consumerMarkDirty(node),
        run,
        cleanup: () => node.cleanupFn(),
        destroy: () => destroyWatchNode(node),
        [SIGNAL]: node,
    };
    return node.ref;
}
const NOOP_CLEANUP_FN = () => { };
// Note: Using an IIFE here to ensure that the spread assignment is not considered
// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.
// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.
const WATCH_NODE = /* @__PURE__ */ (() => {
    return {
        ...REACTIVE_NODE,
        consumerIsAlwaysLive: true,
        consumerAllowSignalWrites: false,
        consumerMarkedDirty: (node) => {
            if (node.schedule !== null) {
                node.schedule(node.ref);
            }
        },
        hasRun: false,
        cleanupFn: NOOP_CLEANUP_FN,
    };
})();
//# sourceMappingURL=data:application/json;base64,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