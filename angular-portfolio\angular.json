{"version": 1, "newProjectRoot": "projects", "projects": {"angular-portfolio": {"projectType": "application", "root": "src", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/angular-portfolio", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css", "src/assets/styles/main.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2kb", "maximumError": "5kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "angular-portfolio:build"}, "configurations": {"production": {"browserTarget": "angular-portfolio:build:production"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "tsconfig.app.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "angular-portfolio"}