{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nlet FooterComponent = class FooterComponent {\n  constructor() {\n    this.currentYear = new Date().getFullYear();\n  }\n  scrollToSection(sectionId) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n  }\n};\nFooterComponent = __decorate([Component({\n  selector: 'app-footer',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <footer class=\"footer\">\n      <div class=\"container\">\n        <div class=\"footer-content\">\n          <div class=\"footer-section\">\n            <h3 class=\"footer-title\">Tourad Dah</h3>\n            <p class=\"footer-description\">\n              Développeur Full-stack passionné par la création d'applications web modernes et sécurisées.\n            </p>\n            <div class=\"social-links\">\n              <a href=\"https://github.com/tourad\" target=\"_blank\" rel=\"noopener\" title=\"GitHub\">\n                <i class=\"fa-brands fa-github\"></i>\n              </a>\n              <a href=\"#\" title=\"Facebook\">\n                <i class=\"fa-brands fa-square-facebook\"></i>\n              </a>\n              <a href=\"#\" target=\"_blank\" rel=\"noopener\" title=\"Twitter\">\n                <i class=\"fa-brands fa-square-x-twitter\"></i>\n              </a>\n              <a href=\"#\" title=\"LinkedIn\">\n                <i class=\"fa-brands fa-linkedin\"></i>\n              </a>\n              <a href=\"http://wa.me/+22238493149\" target=\"_blank\" rel=\"noopener\" title=\"WhatsApp\">\n                <i class=\"fa-brands fa-whatsapp\"></i>\n              </a>\n            </div>\n          </div>\n\n          <div class=\"footer-section\">\n            <h4 class=\"section-title\">Navigation</h4>\n            <ul class=\"footer-links\">\n              <li><a href=\"#home\" (click)=\"scrollToSection('home')\">Accueil</a></li>\n              <li><a href=\"#about\" (click)=\"scrollToSection('about')\">À Propos</a></li>\n              <li><a href=\"#skills\" (click)=\"scrollToSection('skills')\">Compétences</a></li>\n              <li><a href=\"#portfolio\" (click)=\"scrollToSection('portfolio')\">Projets</a></li>\n              <li><a href=\"#contact\" (click)=\"scrollToSection('contact')\">Contact</a></li>\n            </ul>\n          </div>\n\n          <div class=\"footer-section\">\n            <h4 class=\"section-title\">Contact</h4>\n            <div class=\"contact-info\">\n              <div class=\"contact-item\">\n                <i class=\"fas fa-envelope\"></i>\n                <span><EMAIL></span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-phone\"></i>\n                <span>+222 38 49 31 49</span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Nouakchott, Mauritanie</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"footer-bottom\">\n          <div class=\"copyright\">\n            <p>&copy; {{ currentYear }} Tourad Med Mahmoud Dah. Tous droits réservés.</p>\n          </div>\n          <div class=\"footer-links-bottom\">\n            <a href=\"#\" class=\"footer-link\">Politique de confidentialité</a>\n            <a href=\"#\" class=\"footer-link\">Conditions d'utilisation</a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  `,\n  styleUrls: ['./footer.component.scss']\n})], FooterComponent);\nexport { FooterComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FooterComponent", "constructor", "currentYear", "Date", "getFullYear", "scrollToSection", "sectionId", "element", "document", "getElementById", "offsetTop", "window", "scrollTo", "top", "behavior", "__decorate", "selector", "standalone", "imports", "template", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\footer\\footer.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-footer',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <footer class=\"footer\">\n      <div class=\"container\">\n        <div class=\"footer-content\">\n          <div class=\"footer-section\">\n            <h3 class=\"footer-title\">Tourad Dah</h3>\n            <p class=\"footer-description\">\n              Développeur Full-stack passionné par la création d'applications web modernes et sécurisées.\n            </p>\n            <div class=\"social-links\">\n              <a href=\"https://github.com/tourad\" target=\"_blank\" rel=\"noopener\" title=\"GitHub\">\n                <i class=\"fa-brands fa-github\"></i>\n              </a>\n              <a href=\"#\" title=\"Facebook\">\n                <i class=\"fa-brands fa-square-facebook\"></i>\n              </a>\n              <a href=\"#\" target=\"_blank\" rel=\"noopener\" title=\"Twitter\">\n                <i class=\"fa-brands fa-square-x-twitter\"></i>\n              </a>\n              <a href=\"#\" title=\"LinkedIn\">\n                <i class=\"fa-brands fa-linkedin\"></i>\n              </a>\n              <a href=\"http://wa.me/+22238493149\" target=\"_blank\" rel=\"noopener\" title=\"WhatsApp\">\n                <i class=\"fa-brands fa-whatsapp\"></i>\n              </a>\n            </div>\n          </div>\n\n          <div class=\"footer-section\">\n            <h4 class=\"section-title\">Navigation</h4>\n            <ul class=\"footer-links\">\n              <li><a href=\"#home\" (click)=\"scrollToSection('home')\">Accueil</a></li>\n              <li><a href=\"#about\" (click)=\"scrollToSection('about')\">À Propos</a></li>\n              <li><a href=\"#skills\" (click)=\"scrollToSection('skills')\">Compétences</a></li>\n              <li><a href=\"#portfolio\" (click)=\"scrollToSection('portfolio')\">Projets</a></li>\n              <li><a href=\"#contact\" (click)=\"scrollToSection('contact')\">Contact</a></li>\n            </ul>\n          </div>\n\n          <div class=\"footer-section\">\n            <h4 class=\"section-title\">Contact</h4>\n            <div class=\"contact-info\">\n              <div class=\"contact-item\">\n                <i class=\"fas fa-envelope\"></i>\n                <span><EMAIL></span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-phone\"></i>\n                <span>+222 38 49 31 49</span>\n              </div>\n              <div class=\"contact-item\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Nouakchott, Mauritanie</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"footer-bottom\">\n          <div class=\"copyright\">\n            <p>&copy; {{ currentYear }} Tourad Med Mahmoud Dah. Tous droits réservés.</p>\n          </div>\n          <div class=\"footer-links-bottom\">\n            <a href=\"#\" class=\"footer-link\">Politique de confidentialité</a>\n            <a href=\"#\" class=\"footer-link\">Conditions d'utilisation</a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  `,\n  styleUrls: ['./footer.component.scss']\n})\nexport class FooterComponent {\n  currentYear = new Date().getFullYear();\n\n  scrollToSection(sectionId: string) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,YAAY,QAAQ,iBAAiB;AA8EvC,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAArBC,YAAA;IACL,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EAYxC;EAVEC,eAAeA,CAACC,SAAiB;IAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACX,MAAMG,SAAS,GAAGH,OAAO,CAACG,SAAS,GAAG,EAAE;MACxCC,MAAM,CAACC,QAAQ,CAAC;QACdC,GAAG,EAAEH,SAAS;QACdI,QAAQ,EAAE;OACX,CAAC;;EAEN;CACD;AAbYd,eAAe,GAAAe,UAAA,EA5E3BjB,SAAS,CAAC;EACTkB,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACnB,YAAY,CAAC;EACvBoB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqET;EACDC,SAAS,EAAE,CAAC,yBAAyB;CACtC,CAAC,C,EACWpB,eAAe,CAa3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}