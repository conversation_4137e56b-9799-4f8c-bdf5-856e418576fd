/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ChangeDetectionStrategy } from '../change_detection/constants';
import { compileComponent, compileDirective } from '../render3/jit/directive';
import { compilePipe } from '../render3/jit/pipe';
import { makeDecorator, makePropDecorator } from '../util/decorators';
/**
 * Type of the Directive metadata.
 *
 * @publicApi
 */
export const Directive = makeDecorator('Directive', (dir = {}) => dir, undefined, undefined, (type, meta) => compileDirective(type, meta));
/**
 * Component decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Component = makeDecorator('Component', (c = {}) => ({ changeDetection: ChangeDetectionStrategy.Default, ...c }), Directive, undefined, (type, meta) => compileComponent(type, meta));
/**
 * @Annotation
 * @publicApi
 */
export const Pipe = makeDecorator('Pipe', (p) => ({ pure: true, ...p }), undefined, undefined, (type, meta) => compilePipe(type, meta));
/**
 * @Annotation
 * @publicApi
 */
export const Input = makePropDecorator('Input', (arg) => {
    if (!arg) {
        return {};
    }
    return typeof arg === 'string' ? { alias: arg } : arg;
});
/**
 * @Annotation
 * @publicApi
 */
export const Output = makePropDecorator('Output', (alias) => ({ alias }));
/**
 * @Annotation
 * @publicApi
 */
export const HostBinding = makePropDecorator('HostBinding', (hostPropertyName) => ({ hostPropertyName }));
/**
 * Decorator that binds a DOM event to a host listener and supplies configuration metadata.
 * Angular invokes the supplied handler method when the host element emits the specified event,
 * and updates the bound element with the result.
 *
 * If the handler method returns false, applies `preventDefault` on the bound element.
 *
 * @usageNotes
 *
 * The following example declares a directive
 * that attaches a click listener to a button and counts clicks.
 *
 * ```ts
 * @Directive({selector: 'button[counting]'})
 * class CountClicks {
 *   numberOfClicks = 0;
 *
 *   @HostListener('click', ['$event.target'])
 *   onClick(btn) {
 *     console.log('button', btn, 'number of clicks:', this.numberOfClicks++);
 *   }
 * }
 *
 * @Component({
 *   selector: 'app',
 *   template: '<button counting>Increment</button>',
 * })
 * class App {}
 *
 * ```
 *
 * The following example registers another DOM event handler that listens for `Enter` key-press
 * events on the global `window`.
 * ``` ts
 * import { HostListener, Component } from "@angular/core";
 *
 * @Component({
 *   selector: 'app',
 *   template: `<h1>Hello, you have pressed enter {{counter}} number of times!</h1> Press enter key
 * to increment the counter.
 *   <button (click)="resetCounter()">Reset Counter</button>`
 * })
 * class AppComponent {
 *   counter = 0;
 *   @HostListener('window:keydown.enter', ['$event'])
 *   handleKeyDown(event: KeyboardEvent) {
 *     this.counter++;
 *   }
 *   resetCounter() {
 *     this.counter = 0;
 *   }
 * }
 * ```
 * The list of valid key names for `keydown` and `keyup` events
 * can be found here:
 * https://www.w3.org/TR/DOM-Level-3-Events-key/#named-key-attribute-values
 *
 * Note that keys can also be combined, e.g. `@HostListener('keydown.shift.a')`.
 *
 * The global target names that can be used to prefix an event name are
 * `document:`, `window:` and `body:`.
 *
 * @Annotation
 * @publicApi
 */
export const HostListener = makePropDecorator('HostListener', (eventName, args) => ({ eventName, args }));
//# sourceMappingURL=data:application/json;base64,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