{"name": "update-browserslist-db", "version": "1.1.3", "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "keywords": ["caniuse", "browsers", "target"], "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "browserslist/update-db", "types": "./index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json"}, "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "peerDependencies": {"browserslist": ">= 4.21.0"}, "bin": "cli.js"}