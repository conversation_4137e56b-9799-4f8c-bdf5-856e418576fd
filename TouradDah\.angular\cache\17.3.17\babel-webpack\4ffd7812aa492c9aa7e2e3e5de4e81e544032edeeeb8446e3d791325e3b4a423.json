{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => (taking || (taking = !predicate(value, index++))) && subscriber.next(value)));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "source", "subscriber", "taking", "index", "subscribe", "value", "next"], "sources": ["C:/Users/<USER>/Desktop/Mywebsite/TouradDah/node_modules/rxjs/dist/esm/internal/operators/skipWhile.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n    return operate((source, subscriber) => {\n        let taking = false;\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => (taking || (taking = !predicate(value, index++))) && subscriber.next(value)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,SAASA,CAACC,SAAS,EAAE;EACjC,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,KAAK,GAAG,CAAC;IACbH,MAAM,CAACI,SAAS,CAACP,wBAAwB,CAACI,UAAU,EAAGI,KAAK,IAAK,CAACH,MAAM,KAAKA,MAAM,GAAG,CAACH,SAAS,CAACM,KAAK,EAAEF,KAAK,EAAE,CAAC,CAAC,KAAKF,UAAU,CAACK,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;EAClJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}