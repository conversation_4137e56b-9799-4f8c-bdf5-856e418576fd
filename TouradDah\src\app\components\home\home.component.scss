.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-primary) 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
  }
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.content-text {
  animation: fadeInLeft 1s ease-out;
}

.greeting-section {
  margin-bottom: 2rem;
}

.greeting {
  font-size: 18px;
  margin-bottom: 1rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
}

.hero-name {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
  line-height: 1.2;
  border-right: 3px solid var(--primary-color);
  padding-right: 10px;
  animation: blink 1s infinite;
}

.title {
  font-size: 1.8rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1rem;
  border-right: 3px solid var(--primary-color);
  padding-right: 10px;
  animation: blink 1s infinite;
}

.subtitle {
  font-size: 1.2rem;
  color: var(--gray);
  margin-bottom: 2rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
}

.skills-highlight {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.8s forwards;
}

.skill-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--card-bg);
  padding: 0.8rem 1.2rem;
  border-radius: 25px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(32, 201, 151, 0.2);
    border-color: var(--primary-color);
  }

  i {
    color: var(--primary-color);
    font-size: 18px;
  }

  span {
    font-weight: 600;
    color: var(--text-color);
  }
}

.social-links {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 1s forwards;

  a {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    &:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-5px) scale(1.1);
      box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);
    }

    i {
      font-size: 20px;
    }
  }
}

.cta-buttons {
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 1.2s forwards;
}

.btn-primary.enhanced {
  position: relative;
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(32, 201, 151, 0.4);
  }

  .btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover .btn-shine {
    left: 100%;
  }
}

.hero-image {
  display: flex;
  justify-content: center;
  animation: fadeInRight 1s ease-out;
}

.image-container {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.image-container img {
  width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  background: linear-gradient(45deg, rgba(3, 59, 74, 0.1), rgba(32, 201, 151, 0.1));
}

.floating-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  font-weight: 600;
  animation: float 3s ease-in-out infinite;
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes blink {
  0%, 50% { border-color: var(--primary-color); }
  51%, 100% { border-color: transparent; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

// Responsive
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-name {
    font-size: 2.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .skills-highlight {
    justify-content: center;
    flex-wrap: wrap;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-name {
    font-size: 2rem;
  }

  .title {
    font-size: 1.3rem;
  }

  .skills-highlight {
    gap: 0.5rem;
  }

  .skill-badge {
    padding: 0.6rem 1rem;
    font-size: 14px;
  }

  .social-links a {
    width: 45px;
    height: 45px;
  }
}
