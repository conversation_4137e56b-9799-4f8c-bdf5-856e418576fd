/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { SIGNAL_NODE, signalSetFn } from '@angular/core/primitives/signals';
export const REQUIRED_UNSET_VALUE = /* @__PURE__ */ Symbol('InputSignalNode#UNSET');
// Note: Using an IIFE here to ensure that the spread assignment is not considered
// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.
// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.
export const INPUT_SIGNAL_NODE = /* @__PURE__ */ (() => {
    return {
        ...SIGNAL_NODE,
        transformFn: undefined,
        applyValueToInputSignal(node, value) {
            signalSetFn(node, value);
        }
    };
})();
//# sourceMappingURL=data:application/json;base64,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