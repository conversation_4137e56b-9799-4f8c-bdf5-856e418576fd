{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction SkillsComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function SkillsComponent_button_8_Template_button_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.filterSkills(category_r2.id));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", category_r2.active);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r2.name, \" \");\n  }\n}\nfunction SkillsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"h3\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 17)(7, \"div\", 18);\n    i0.ɵɵelement(8, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 20);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const skill_r4 = ctx.$implicit;\n    i0.ɵɵattribute(\"data-category\", skill_r4.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", skill_r4.icon, i0.ɵɵsanitizeUrl)(\"alt\", skill_r4.name + \" logo\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(skill_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", skill_r4.level, \"%\");\n    i0.ɵɵattribute(\"data-level\", skill_r4.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r4.level, \"%\");\n  }\n}\nfunction SkillsComponent_div_12_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementStart(3, \"span\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 28);\n    i0.ɵɵelement(8, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skill_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", skill_r5.icon, i0.ɵɵsanitizeUrl)(\"alt\", skill_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(skill_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r5.level, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", skill_r5.level, \"%\");\n  }\n}\nfunction SkillsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, SkillsComponent_div_12_div_4_Template, 9, 6, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSkillsByCategory(category_r6.id));\n  }\n}\nexport class SkillsComponent {\n  constructor() {\n    this.skills = [\n    // Frontend\n    {\n      name: 'HTML5',\n      icon: 'assets/images/html.png',\n      level: 95,\n      category: 'frontend'\n    }, {\n      name: 'CSS3',\n      icon: 'assets/images/css.png',\n      level: 90,\n      category: 'frontend'\n    }, {\n      name: 'JavaScript',\n      icon: 'assets/images/js.png',\n      level: 88,\n      category: 'frontend'\n    }, {\n      name: 'React',\n      icon: 'assets/images/react.png',\n      level: 85,\n      category: 'frontend'\n    }, {\n      name: 'Angular',\n      icon: 'assets/images/angular.png',\n      level: 80,\n      category: 'frontend'\n    }, {\n      name: 'Vue.js',\n      icon: 'assets/images/vue.png',\n      level: 75,\n      category: 'frontend'\n    },\n    // Backend\n    {\n      name: 'Node.js',\n      icon: 'assets/images/nodejs.png',\n      level: 85,\n      category: 'backend'\n    }, {\n      name: 'Python',\n      icon: 'assets/images/python.png',\n      level: 90,\n      category: 'backend'\n    }, {\n      name: 'PHP',\n      icon: 'assets/images/php.png',\n      level: 80,\n      category: 'backend'\n    }, {\n      name: 'Java',\n      icon: 'assets/images/java.png',\n      level: 75,\n      category: 'backend'\n    },\n    // Database\n    {\n      name: 'MySQL',\n      icon: 'assets/images/mysql.png',\n      level: 85,\n      category: 'database'\n    }, {\n      name: 'MongoDB',\n      icon: 'assets/images/mongodb.png',\n      level: 80,\n      category: 'database'\n    }, {\n      name: 'PostgreSQL',\n      icon: 'assets/images/postgresql.png',\n      level: 75,\n      category: 'database'\n    },\n    // Tools\n    {\n      name: 'Git',\n      icon: 'assets/images/git.png',\n      level: 90,\n      category: 'tools'\n    }, {\n      name: 'Docker',\n      icon: 'assets/images/docker.png',\n      level: 70,\n      category: 'tools'\n    }, {\n      name: 'Linux',\n      icon: 'assets/images/linux.png',\n      level: 85,\n      category: 'tools'\n    }];\n    this.categories = [{\n      id: 'all',\n      name: 'Toutes',\n      active: true\n    }, {\n      id: 'frontend',\n      name: 'Frontend',\n      active: false\n    }, {\n      id: 'backend',\n      name: 'Backend',\n      active: false\n    }, {\n      id: 'database',\n      name: 'Base de données',\n      active: false\n    }, {\n      id: 'tools',\n      name: 'Outils',\n      active: false\n    }];\n    this.filteredSkills = [];\n    this.activeCategory = 'all';\n  }\n  ngOnInit() {\n    this.filteredSkills = this.skills;\n  }\n  filterSkills(categoryId) {\n    this.activeCategory = categoryId;\n    // Update active category\n    this.categories.forEach(cat => {\n      cat.active = cat.id === categoryId;\n    });\n    // Filter skills\n    if (categoryId === 'all') {\n      this.filteredSkills = this.skills;\n    } else {\n      this.filteredSkills = this.skills.filter(skill => skill.category === categoryId);\n    }\n  }\n  getSkillsByCategory(category) {\n    return this.skills.filter(skill => skill.category === category);\n  }\n  trackBySkill(index, skill) {\n    return skill.name;\n  }\n  static {\n    this.ɵfac = function SkillsComponent_Factory(t) {\n      return new (t || SkillsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SkillsComponent,\n      selectors: [[\"app-skills\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 4,\n      consts: [[\"id\", \"skills\", 1, \"skills-section\"], [1, \"container\"], [1, \"section-header\", \"animate-on-scroll\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"category-filters\", \"animate-on-scroll\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"skills-grid\", \"animate-on-scroll\"], [\"class\", \"skill-card\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"skills-by-category\", 2, \"display\", \"none\"], [\"class\", \"category-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"filter-btn\", 3, \"click\"], [1, \"skill-card\"], [1, \"skill-icon\"], [3, \"src\", \"alt\"], [1, \"skill-info\"], [1, \"skill-name\"], [1, \"skill-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"skill-percentage\"], [1, \"category-section\"], [1, \"category-title\"], [1, \"category-skills\"], [\"class\", \"skill-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"skill-item\"], [1, \"skill-header\"], [1, \"skill-level\"], [1, \"skill-bar\"], [1, \"skill-fill\"]],\n      template: function SkillsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Mes Comp\\u00E9tences\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Technologies et outils que je ma\\u00EEtrise\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵtemplate(8, SkillsComponent_button_8_Template, 2, 3, \"button\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7);\n          i0.ɵɵtemplate(10, SkillsComponent_div_10_Template, 11, 8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9);\n          i0.ɵɵtemplate(12, SkillsComponent_div_12_Template, 5, 2, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredSkills)(\"ngForTrackBy\", ctx.trackBySkill);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories.slice(1));\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf],\n      styles: [\".skills-section[_ngcontent-%COMP%] {\\n  padding: 100px 0;\\n  background: var(--bg-primary);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 3rem;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n  position: relative;\\n}\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: var(--gradient-primary);\\n  border-radius: 2px;\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: var(--gray);\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.category-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 1rem;\\n  margin-bottom: 3rem;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-btn[_ngcontent-%COMP%] {\\n  padding: 0.8rem 1.5rem;\\n  border: 2px solid var(--border-color);\\n  background: var(--card-bg);\\n  color: var(--text-color);\\n  border-radius: 25px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.filter-btn[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n}\\n.filter-btn.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  border-color: var(--primary-color);\\n  color: white;\\n  box-shadow: 0 5px 15px rgba(32, 201, 151, 0.3);\\n}\\n\\n.skills-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 2rem;\\n  margin-bottom: 3rem;\\n}\\n\\n.skill-card[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.skill-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(32, 201, 151, 0.1), transparent);\\n  transition: left 0.5s;\\n}\\n.skill-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  border-color: var(--primary-color);\\n}\\n.skill-card[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.skill-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  margin: 0 auto 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: var(--bg-color);\\n  border-radius: 50%;\\n  border: 3px solid var(--border-color);\\n  transition: all 0.3s ease;\\n}\\n.skill-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  object-fit: contain;\\n}\\n\\n.skill-card[_ngcontent-%COMP%]:hover   .skill-icon[_ngcontent-%COMP%] {\\n  border-color: var(--primary-color);\\n  transform: scale(1.1);\\n}\\n\\n.skill-info[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.skill-name[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 1rem;\\n}\\n\\n.skill-progress[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background: var(--border-color);\\n  border-radius: 4px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--gradient-primary);\\n  border-radius: 4px;\\n  transition: width 1s ease-in-out;\\n  position: relative;\\n}\\n.progress-fill[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.skill-percentage[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  font-size: 0.9rem;\\n  min-width: 40px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 2px solid var(--primary-color);\\n  display: inline-block;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .category-skills[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-item[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  border-radius: 10px;\\n  padding: 1.5rem;\\n  transition: all 0.3s ease;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  transform: translateX(5px);\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  object-fit: contain;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-header[_ngcontent-%COMP%]   .skill-name[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-header[_ngcontent-%COMP%]   .skill-level[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--primary-color);\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-bar[_ngcontent-%COMP%] {\\n  height: 6px;\\n  background: var(--border-color);\\n  border-radius: 3px;\\n  overflow: hidden;\\n}\\n.skills-by-category[_ngcontent-%COMP%]   .skill-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--gradient-primary);\\n  border-radius: 3px;\\n  transition: width 1s ease-in-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .skills-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 1.5rem;\\n  }\\n  .skill-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .skill-icon[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n  .skill-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .category-filters[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .filter-btn[_ngcontent-%COMP%] {\\n    padding: 0.6rem 1.2rem;\\n    font-size: 0.9rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .skills-section[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .skills-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 1rem;\\n  }\\n  .skill-card[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .skill-icon[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .skill-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .skill-name[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .category-filters[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .filter-btn[_ngcontent-%COMP%] {\\n    width: 200px;\\n    text-align: center;\\n  }\\n}\\n@media (max-width: 375px) {\\n  .skills-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n    gap: 0.8rem;\\n  }\\n  .skill-card[_ngcontent-%COMP%] {\\n    padding: 0.8rem;\\n  }\\n  .skill-icon[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    margin-bottom: 1rem;\\n  }\\n  .skill-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .skill-name[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-bottom: 0.8rem;\\n  }\\n  .skill-progress[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .progress-bar[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SkillsComponent_button_8_Template_button_click_0_listener", "category_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "filterSkills", "id", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "active", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "ɵɵelement", "ɵɵproperty", "skill_r4", "icon", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵstyleProp", "level", "skill_r5", "ɵɵtemplate", "SkillsComponent_div_12_div_4_Template", "category_r6", "getSkillsByCategory", "SkillsComponent", "constructor", "skills", "category", "categories", "filteredSkills", "activeCategory", "ngOnInit", "categoryId", "for<PERSON>ach", "cat", "filter", "skill", "trackBySkill", "index", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SkillsComponent_Template", "rf", "ctx", "SkillsComponent_button_8_Template", "SkillsComponent_div_10_Template", "SkillsComponent_div_12_Template", "slice", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\skills\\skills.component.ts", "C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\skills\\skills.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\ninterface Skill {\n  name: string;\n  icon: string;\n  level: number;\n  category: string;\n}\n\n@Component({\n  selector: 'app-skills',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './skills.component.html',\n  styleUrls: ['./skills.component.scss']\n})\nexport class SkillsComponent implements OnInit {\n  \n  skills: Skill[] = [\n    // Frontend\n    { name: 'HTML5', icon: 'assets/images/html.png', level: 95, category: 'frontend' },\n    { name: 'CSS3', icon: 'assets/images/css.png', level: 90, category: 'frontend' },\n    { name: 'JavaScript', icon: 'assets/images/js.png', level: 88, category: 'frontend' },\n    { name: 'React', icon: 'assets/images/react.png', level: 85, category: 'frontend' },\n    { name: 'Angular', icon: 'assets/images/angular.png', level: 80, category: 'frontend' },\n    { name: 'Vue.js', icon: 'assets/images/vue.png', level: 75, category: 'frontend' },\n    \n    // Backend\n    { name: 'Node.js', icon: 'assets/images/nodejs.png', level: 85, category: 'backend' },\n    { name: 'Python', icon: 'assets/images/python.png', level: 90, category: 'backend' },\n    { name: 'PHP', icon: 'assets/images/php.png', level: 80, category: 'backend' },\n    { name: 'Java', icon: 'assets/images/java.png', level: 75, category: 'backend' },\n    \n    // Database\n    { name: 'MySQL', icon: 'assets/images/mysql.png', level: 85, category: 'database' },\n    { name: 'MongoDB', icon: 'assets/images/mongodb.png', level: 80, category: 'database' },\n    { name: 'PostgreSQL', icon: 'assets/images/postgresql.png', level: 75, category: 'database' },\n    \n    // Tools\n    { name: 'Git', icon: 'assets/images/git.png', level: 90, category: 'tools' },\n    { name: 'Docker', icon: 'assets/images/docker.png', level: 70, category: 'tools' },\n    { name: 'Linux', icon: 'assets/images/linux.png', level: 85, category: 'tools' }\n  ];\n\n  categories = [\n    { id: 'all', name: 'Toutes', active: true },\n    { id: 'frontend', name: 'Frontend', active: false },\n    { id: 'backend', name: 'Backend', active: false },\n    { id: 'database', name: 'Base de données', active: false },\n    { id: 'tools', name: 'Outils', active: false }\n  ];\n\n  filteredSkills: Skill[] = [];\n  activeCategory = 'all';\n\n  ngOnInit() {\n    this.filteredSkills = this.skills;\n  }\n\n  filterSkills(categoryId: string) {\n    this.activeCategory = categoryId;\n    \n    // Update active category\n    this.categories.forEach(cat => {\n      cat.active = cat.id === categoryId;\n    });\n\n    // Filter skills\n    if (categoryId === 'all') {\n      this.filteredSkills = this.skills;\n    } else {\n      this.filteredSkills = this.skills.filter(skill => skill.category === categoryId);\n    }\n  }\n\n  getSkillsByCategory(category: string): Skill[] {\n    return this.skills.filter(skill => skill.category === category);\n  }\n\n  trackBySkill(index: number, skill: Skill): string {\n    return skill.name;\n  }\n}\n", "<section id=\"skills\" class=\"skills-section\">\n  <div class=\"container\">\n    <div class=\"section-header animate-on-scroll\">\n      <h2 class=\"section-title\">Mes Compétences</h2>\n      <p class=\"section-subtitle\">Technologies et outils que je maîtrise</p>\n    </div>\n\n    <!-- Filtres de catégories -->\n    <div class=\"category-filters animate-on-scroll\">\n      <button \n        *ngFor=\"let category of categories\"\n        class=\"filter-btn\"\n        [class.active]=\"category.active\"\n        (click)=\"filterSkills(category.id)\">\n        {{ category.name }}\n      </button>\n    </div>\n\n    <!-- Grille des compétences -->\n    <div class=\"skills-grid animate-on-scroll\">\n      <div \n        *ngFor=\"let skill of filteredSkills; trackBy: trackBySkill\"\n        class=\"skill-card\"\n        [attr.data-category]=\"skill.category\">\n        \n        <div class=\"skill-icon\">\n          <img [src]=\"skill.icon\" [alt]=\"skill.name + ' logo'\">\n        </div>\n        \n        <div class=\"skill-info\">\n          <h3 class=\"skill-name\">{{ skill.name }}</h3>\n          <div class=\"skill-progress\">\n            <div class=\"progress-bar\">\n              <div \n                class=\"progress-fill\" \n                [style.width.%]=\"skill.level\"\n                [attr.data-level]=\"skill.level\">\n              </div>\n            </div>\n            <span class=\"skill-percentage\">{{ skill.level }}%</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Section des compétences par catégorie (alternative) -->\n    <div class=\"skills-by-category\" style=\"display: none;\">\n      <div class=\"category-section\" *ngFor=\"let category of categories.slice(1)\">\n        <h3 class=\"category-title\">{{ category.name }}</h3>\n        <div class=\"category-skills\">\n          <div \n            *ngFor=\"let skill of getSkillsByCategory(category.id)\"\n            class=\"skill-item\">\n            <div class=\"skill-header\">\n              <img [src]=\"skill.icon\" [alt]=\"skill.name\">\n              <span class=\"skill-name\">{{ skill.name }}</span>\n              <span class=\"skill-level\">{{ skill.level }}%</span>\n            </div>\n            <div class=\"skill-bar\">\n              <div \n                class=\"skill-fill\" \n                [style.width.%]=\"skill.level\">\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;ICQxCC,EAAA,CAAAC,cAAA,iBAIsC;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,WAAA,CAAAQ,EAAA,CAAyB;IAAA,EAAC;IACnCZ,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAHPd,EAAA,CAAAe,WAAA,WAAAX,WAAA,CAAAY,MAAA,CAAgC;IAEhChB,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAd,WAAA,CAAAe,IAAA,MACF;;;;;IAUEnB,EALF,CAAAC,cAAA,cAGwC,cAEd;IACtBD,EAAA,CAAAoB,SAAA,cAAqD;IACvDpB,EAAA,CAAAc,YAAA,EAAM;IAGJd,EADF,CAAAC,cAAA,cAAwB,aACC;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAE1Cd,EADF,CAAAC,cAAA,cAA4B,cACA;IACxBD,EAAA,CAAAoB,SAAA,cAIM;IACRpB,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAkB;IAGvDb,EAHuD,CAAAc,YAAA,EAAO,EACpD,EACF,EACF;;;;;IAhBGd,EAAA,CAAAiB,SAAA,GAAkB;IAACjB,EAAnB,CAAAqB,UAAA,QAAAC,QAAA,CAAAC,IAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAkB,QAAAF,QAAA,CAAAH,IAAA,WAA6B;IAI7BnB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAyB,iBAAA,CAAAH,QAAA,CAAAH,IAAA,CAAgB;IAKjCnB,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAA0B,WAAA,UAAAJ,QAAA,CAAAK,KAAA,MAA6B;;IAIF3B,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,kBAAA,KAAAI,QAAA,CAAAK,KAAA,MAAkB;;;;;IAcjD3B,EAHF,CAAAC,cAAA,cAEqB,cACO;IACxBD,EAAA,CAAAoB,SAAA,cAA2C;IAC3CpB,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAChDd,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,GAAkB;IAC9Cb,EAD8C,CAAAc,YAAA,EAAO,EAC/C;IACNd,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAoB,SAAA,cAGM;IAEVpB,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAVGd,EAAA,CAAAiB,SAAA,GAAkB;IAACjB,EAAnB,CAAAqB,UAAA,QAAAO,QAAA,CAAAL,IAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAkB,QAAAI,QAAA,CAAAT,IAAA,CAAmB;IACjBnB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAyB,iBAAA,CAAAG,QAAA,CAAAT,IAAA,CAAgB;IACfnB,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,kBAAA,KAAAU,QAAA,CAAAD,KAAA,MAAkB;IAK1C3B,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAA0B,WAAA,UAAAE,QAAA,CAAAD,KAAA,MAA6B;;;;;IAbrC3B,EADF,CAAAC,cAAA,cAA2E,aAC9C;IAAAD,EAAA,CAAAa,MAAA,GAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACnDd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAA6B,UAAA,IAAAC,qCAAA,kBAEqB;IAczB9B,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IAlBuBd,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAyB,iBAAA,CAAAM,WAAA,CAAAZ,IAAA,CAAmB;IAGxBnB,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAwB,mBAAA,CAAAD,WAAA,CAAAnB,EAAA,EAAmC;;;ADlCjE,OAAM,MAAOqB,eAAe;EAP5BC,YAAA;IASE,KAAAC,MAAM,GAAY;IAChB;IACA;MAAEhB,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,wBAAwB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EAClF;MAAEjB,IAAI,EAAE,MAAM;MAAEI,IAAI,EAAE,uBAAuB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EAChF;MAAEjB,IAAI,EAAE,YAAY;MAAEI,IAAI,EAAE,sBAAsB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EACrF;MAAEjB,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,yBAAyB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EACnF;MAAEjB,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,2BAA2B;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EACvF;MAAEjB,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,uBAAuB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE;IAElF;IACA;MAAEjB,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,0BAA0B;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAS,CAAE,EACrF;MAAEjB,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,0BAA0B;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAS,CAAE,EACpF;MAAEjB,IAAI,EAAE,KAAK;MAAEI,IAAI,EAAE,uBAAuB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAS,CAAE,EAC9E;MAAEjB,IAAI,EAAE,MAAM;MAAEI,IAAI,EAAE,wBAAwB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAS,CAAE;IAEhF;IACA;MAAEjB,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,yBAAyB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EACnF;MAAEjB,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,2BAA2B;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE,EACvF;MAAEjB,IAAI,EAAE,YAAY;MAAEI,IAAI,EAAE,8BAA8B;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAU,CAAE;IAE7F;IACA;MAAEjB,IAAI,EAAE,KAAK;MAAEI,IAAI,EAAE,uBAAuB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAO,CAAE,EAC5E;MAAEjB,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,0BAA0B;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAO,CAAE,EAClF;MAAEjB,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,yBAAyB;MAAEI,KAAK,EAAE,EAAE;MAAES,QAAQ,EAAE;IAAO,CAAE,CACjF;IAED,KAAAC,UAAU,GAAG,CACX;MAAEzB,EAAE,EAAE,KAAK;MAAEO,IAAI,EAAE,QAAQ;MAAEH,MAAM,EAAE;IAAI,CAAE,EAC3C;MAAEJ,EAAE,EAAE,UAAU;MAAEO,IAAI,EAAE,UAAU;MAAEH,MAAM,EAAE;IAAK,CAAE,EACnD;MAAEJ,EAAE,EAAE,SAAS;MAAEO,IAAI,EAAE,SAAS;MAAEH,MAAM,EAAE;IAAK,CAAE,EACjD;MAAEJ,EAAE,EAAE,UAAU;MAAEO,IAAI,EAAE,iBAAiB;MAAEH,MAAM,EAAE;IAAK,CAAE,EAC1D;MAAEJ,EAAE,EAAE,OAAO;MAAEO,IAAI,EAAE,QAAQ;MAAEH,MAAM,EAAE;IAAK,CAAE,CAC/C;IAED,KAAAsB,cAAc,GAAY,EAAE;IAC5B,KAAAC,cAAc,GAAG,KAAK;;EAEtBC,QAAQA,CAAA;IACN,IAAI,CAACF,cAAc,GAAG,IAAI,CAACH,MAAM;EACnC;EAEAxB,YAAYA,CAAC8B,UAAkB;IAC7B,IAAI,CAACF,cAAc,GAAGE,UAAU;IAEhC;IACA,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,IAAG;MAC5BA,GAAG,CAAC3B,MAAM,GAAG2B,GAAG,CAAC/B,EAAE,KAAK6B,UAAU;IACpC,CAAC,CAAC;IAEF;IACA,IAAIA,UAAU,KAAK,KAAK,EAAE;MACxB,IAAI,CAACH,cAAc,GAAG,IAAI,CAACH,MAAM;KAClC,MAAM;MACL,IAAI,CAACG,cAAc,GAAG,IAAI,CAACH,MAAM,CAACS,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACT,QAAQ,KAAKK,UAAU,CAAC;;EAEpF;EAEAT,mBAAmBA,CAACI,QAAgB;IAClC,OAAO,IAAI,CAACD,MAAM,CAACS,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACT,QAAQ,KAAKA,QAAQ,CAAC;EACjE;EAEAU,YAAYA,CAACC,KAAa,EAAEF,KAAY;IACtC,OAAOA,KAAK,CAAC1B,IAAI;EACnB;;;uBAjEWc,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAe,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlD,EAAA,CAAAmD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdtBzD,EAHN,CAAAC,cAAA,iBAA4C,aACnB,aACyB,YAClB;UAAAD,EAAA,CAAAa,MAAA,2BAAe;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC9Cd,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAa,MAAA,kDAAsC;UACpEb,EADoE,CAAAc,YAAA,EAAI,EAClE;UAGNd,EAAA,CAAAC,cAAA,aAAgD;UAC9CD,EAAA,CAAA6B,UAAA,IAAA8B,iCAAA,oBAIsC;UAGxC3D,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAC,cAAA,aAA2C;UACzCD,EAAA,CAAA6B,UAAA,KAAA+B,+BAAA,kBAGwC;UAoB1C5D,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAC,cAAA,cAAuD;UACrDD,EAAA,CAAA6B,UAAA,KAAAgC,+BAAA,kBAA2E;UAsBjF7D,EAFI,CAAAc,YAAA,EAAM,EACF,EACE;;;UA3DmBd,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAqB,UAAA,YAAAqC,GAAA,CAAArB,UAAA,CAAa;UAWhBrC,EAAA,CAAAiB,SAAA,GAAmB;UAAAjB,EAAnB,CAAAqB,UAAA,YAAAqC,GAAA,CAAApB,cAAA,CAAmB,iBAAAoB,GAAA,CAAAZ,YAAA,CAAqB;UA0BT9C,EAAA,CAAAiB,SAAA,GAAsB;UAAtBjB,EAAA,CAAAqB,UAAA,YAAAqC,GAAA,CAAArB,UAAA,CAAAyB,KAAA,IAAsB;;;qBDlCnE/D,YAAY,EAAAgE,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}