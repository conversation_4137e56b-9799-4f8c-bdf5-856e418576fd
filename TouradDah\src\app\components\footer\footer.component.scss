.footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: 3rem 0 1rem;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-section {
  .footer-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
  }

  .footer-description {
    color: var(--gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.social-links {
  display: flex;
  gap: 1rem;

  a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(32, 201, 151, 0.3);
    }

    i {
      font-size: 16px;
    }
  }
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: 0.5rem;

    a {
      color: var(--gray);
      text-decoration: none;
      transition: all 0.3s ease;
      display: inline-block;

      &:hover {
        color: var(--primary-color);
        transform: translateX(5px);
      }
    }
  }
}

.contact-info {
  .contact-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
    color: var(--gray);

    i {
      color: var(--primary-color);
      width: 16px;
      text-align: center;
    }

    span {
      font-size: 0.9rem;
    }
  }
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.copyright {
  p {
    color: var(--gray);
    font-size: 0.9rem;
    margin: 0;
  }
}

.footer-links-bottom {
  display: flex;
  gap: 2rem;

  .footer-link {
    color: var(--gray);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;

    &:hover {
      color: var(--primary-color);
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-links-bottom {
    justify-content: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2rem 0 1rem;
  }

  .footer-content {
    gap: 1.5rem;
  }

  .footer-section {
    .footer-title {
      font-size: 1.5rem;
    }

    .section-title {
      font-size: 1.1rem;
    }
  }

  .social-links a {
    width: 35px;
    height: 35px;

    i {
      font-size: 14px;
    }
  }

  .footer-links-bottom {
    flex-direction: column;
    gap: 1rem;
  }
}
