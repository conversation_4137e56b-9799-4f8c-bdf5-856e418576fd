# Portfolio Tourad Dah - Angular 17

Portfolio personnel de Tourad <PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON> full-stack spécialisé en cybersécurité, converti en Angular 17.

## 🚀 Fonctionnalités

- **Design Responsive** : Optimisé pour tous les appareils (desktop, tablette, mobile)
- **Mode Sombre/Clair** : Basculement entre les thèmes avec sauvegarde des préférences
- **Animations Fluides** : Animations CSS et JavaScript pour une expérience utilisateur moderne
- **Navigation Smooth** : Navigation fluide entre les sections
- **Composants Modulaires** : Architecture Angular avec composants standalone
- **TypeScript** : Code typé pour une meilleure maintenabilité
- **SCSS** : Styles organisés avec variables CSS et mixins

## 🛠️ Technologies Utilisées

- **Frontend** : Angular 17, TypeScript, SCSS
- **Animations** : CSS Animations, Angular Animations
- **Icons** : Font Awesome, Boxicons
- **Build** : Angular CLI, Webpack

## 📋 Prérequis

Avant de commencer, assurez-vous d'avoir installé :

- [Node.js](https://nodejs.org/) (version 18 ou supérieure)
- [npm](https://www.npmjs.com/) (inclus avec Node.js)
- [Angular CLI](https://angular.io/cli) (optionnel mais recommandé)

## 🔧 Installation

1. **Cloner le projet** (ou naviguer vers le dossier)
   ```bash
   cd TouradDah
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Installer Angular CLI globalement** (si pas déjà fait)
   ```bash
   npm install -g @angular/cli@17
   ```

## 🚀 Démarrage

### Développement

Pour démarrer le serveur de développement :

```bash
npm start
# ou
ng serve
```

L'application sera accessible sur `http://localhost:4200`

### Production

Pour construire l'application pour la production :

```bash
npm run build
# ou
ng build --configuration production
```

Les fichiers de production seront générés dans le dossier `dist/`

## 📁 Structure du Projet

```
TouradDah/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   ├── navbar/          # Navigation principale
│   │   │   ├── home/            # Section d'accueil
│   │   │   ├── about/           # Section à propos
│   │   │   ├── skills/          # Compétences techniques
│   │   │   ├── portfolio/       # Projets réalisés
│   │   │   ├── education/       # Formation et expérience
│   │   │   ├── contact/         # Formulaire de contact
│   │   │   ├── footer/          # Pied de page
│   │   │   └── scroll-to-top/   # Bouton retour en haut
│   │   ├── app.component.*      # Composant racine
│   │   ├── app.config.ts        # Configuration Angular
│   │   └── app.routes.ts        # Configuration des routes
│   ├── assets/
│   │   └── images/              # Images du portfolio
│   ├── styles.scss              # Styles globaux
│   ├── index.html               # Page HTML principale
│   └── main.ts                  # Point d'entrée de l'application
├── angular.json                 # Configuration Angular CLI
├── package.json                 # Dépendances et scripts
├── tsconfig.json               # Configuration TypeScript
└── README.md                   # Documentation
```

## 🎨 Personnalisation

### Thèmes et Couleurs

Les variables CSS sont définies dans `src/styles.scss` :

```scss
:root {
  --primary-color: #033b4a;
  --secondar-color: #20c997;
  --bg-color: #ffffff;
  --text-color: #333333;
  // ...
}
```

### Contenu

- **Informations personnelles** : Modifiez les composants dans `src/app/components/`
- **Images** : Remplacez les images dans `src/assets/images/`
- **Projets** : Éditez le tableau `projects` dans `portfolio.component.ts`
- **Compétences** : Modifiez le tableau `skills` dans `skills.component.ts`

## 📱 Responsive Design

Le portfolio est optimisé pour :
- **Desktop** : 1200px et plus
- **Tablette** : 768px - 1199px
- **Mobile** : 320px - 767px

## 🔧 Scripts Disponibles

- `npm start` : Démarre le serveur de développement
- `npm run build` : Construit l'application pour la production
- `npm run watch` : Construit en mode watch pour le développement
- `npm test` : Lance les tests unitaires
- `npm run serve` : Démarre le serveur avec configuration personnalisée

## 🌐 Déploiement

### Vercel (Recommandé)

1. Connectez votre repository GitHub à Vercel
2. Vercel détectera automatiquement Angular
3. Déployez avec les paramètres par défaut

### Netlify

1. Construisez le projet : `npm run build`
2. Uploadez le contenu du dossier `dist/tourad-portfolio/`
3. Configurez les redirections pour le SPA

### GitHub Pages

1. Installez angular-cli-ghpages : `npm install -g angular-cli-ghpages`
2. Construisez et déployez : `ng deploy --base-href=/nom-du-repo/`

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :

1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Contact

**Tourad Med Mahmoud Dah**
- Email : <EMAIL>
- Téléphone : +222 38 49 31 49
- GitHub : [https://github.com/tourad](https://github.com/tourad)
- WhatsApp : [http://wa.me/+22238493149](http://wa.me/+22238493149)

---

Développé avec ❤️ par Tourad Dah
