/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isForwardRef, resolveForwardRef } from '../di/forward_ref';
import { flatten } from '../util/array_utils';
import { noSideEffects } from '../util/closure';
import { EMPTY_ARRAY } from '../util/empty';
import { extractDefListOrFactory, getNgModuleDef } from './definition';
import { depsTracker } from './deps_tracker/deps_tracker';
import { isModuleWithProviders } from './jit/util';
/**
 * Generated next to NgModules to monkey-patch directive and pipe references onto a component's
 * definition, when generating a direct reference in the component file would otherwise create an
 * import cycle.
 *
 * See [this explanation](https://hackmd.io/Odw80D0pR6yfsOjg_7XCJg?view) for more details.
 *
 * @codeGenApi
 */
export function ɵɵsetComponentScope(type, directives, pipes) {
    const def = type.ɵcmp;
    def.directiveDefs = extractDefListOrFactory(directives, /* pipeDef */ false);
    def.pipeDefs = extractDefListOrFactory(pipes, /* pipeDef */ true);
}
/**
 * Adds the module metadata that is necessary to compute the module's transitive scope to an
 * existing module definition.
 *
 * Scope metadata of modules is not used in production builds, so calls to this function can be
 * marked pure to tree-shake it from the bundle, allowing for all referenced declarations
 * to become eligible for tree-shaking as well.
 *
 * @codeGenApi
 */
export function ɵɵsetNgModuleScope(type, scope) {
    return noSideEffects(() => {
        const ngModuleDef = getNgModuleDef(type, true);
        ngModuleDef.declarations = convertToTypeArray(scope.declarations || EMPTY_ARRAY);
        ngModuleDef.imports = convertToTypeArray(scope.imports || EMPTY_ARRAY);
        ngModuleDef.exports = convertToTypeArray(scope.exports || EMPTY_ARRAY);
        if (scope.bootstrap) {
            // This only happens in local compilation mode.
            ngModuleDef.bootstrap = convertToTypeArray(scope.bootstrap);
        }
        depsTracker.registerNgModule(type, scope);
    });
}
function convertToTypeArray(values) {
    if (typeof values === 'function') {
        return values;
    }
    const flattenValues = flatten(values);
    if (flattenValues.some(isForwardRef)) {
        return () => flattenValues.map(resolveForwardRef).map(maybeUnwrapModuleWithProviders);
    }
    else {
        return flattenValues.map(maybeUnwrapModuleWithProviders);
    }
}
function maybeUnwrapModuleWithProviders(value) {
    return isModuleWithProviders(value) ? value.ngModule : value;
}
//# sourceMappingURL=data:application/json;base64,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