.portfolio-section {
    padding: 50px 0;
    background-color: #f9f9f9;
}

.portfolio-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.portfolio-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.portfolio-card:hover {
    transform: translateY(-5px);
}

.portfolio-image img {
    width: 100%;
    height: auto;
}

.portfolio-content {
    padding: 15px;
}

.portfolio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.project-status {
    font-weight: bold;
    color: green;
}

.portfolio-description-text {
    margin: 10px 0;
}

.portfolio-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.tech-tag {
    background-color: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9rem;
}

.portfolio-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 0.8rem;
    color: #666;
}