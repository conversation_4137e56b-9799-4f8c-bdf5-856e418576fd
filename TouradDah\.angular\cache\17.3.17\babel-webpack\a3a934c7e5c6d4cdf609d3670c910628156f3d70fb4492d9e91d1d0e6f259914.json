{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nlet ContactComponent = class ContactComponent {\n  constructor() {\n    this.formData = {\n      name: '',\n      email: '',\n      subject: '',\n      message: ''\n    };\n    this.isSubmitting = false;\n    this.submitStatus = null;\n  }\n  onSubmit() {\n    if (this.isSubmitting) return;\n    this.isSubmitting = true;\n    this.submitStatus = null;\n    // Simulation d'envoi (remplacer par vraie logique d'envoi)\n    setTimeout(() => {\n      this.isSubmitting = false;\n      this.submitStatus = {\n        type: 'success',\n        message: 'Message envoyé avec succès ! Je vous répondrai bientôt.',\n        icon: 'fas fa-check-circle'\n      };\n      // Reset form\n      this.formData = {\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      };\n      // Clear status after 5 seconds\n      setTimeout(() => {\n        this.submitStatus = null;\n      }, 5000);\n    }, 2000);\n  }\n};\nContactComponent = __decorate([Component({\n  selector: 'app-contact',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <section id=\"contact\" class=\"contact-section\">\n      <div class=\"container\">\n        <div class=\"section-header animate-on-scroll\">\n          <h2 class=\"section-title\">Contactez-moi</h2>\n          <p class=\"section-subtitle\">N'hésitez pas à me contacter pour discuter de vos projets</p>\n        </div>\n\n        <div class=\"contact-content\">\n          <!-- Informations de contact -->\n          <div class=\"contact-info animate-on-scroll\">\n            <h3 class=\"info-title\">Restons en contact</h3>\n            <p class=\"info-description\">\n              Je suis toujours ouvert aux nouvelles opportunités et collaborations. \n              Contactez-moi pour discuter de vos projets !\n            </p>\n\n            <div class=\"contact-methods\">\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fas fa-envelope\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>Email</h4>\n                  <a href=\"mailto:<EMAIL>\"><EMAIL></a>\n                </div>\n              </div>\n\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fas fa-phone\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>Téléphone</h4>\n                  <a href=\"tel:+22238493149\">+222 38 49 31 49</a>\n                </div>\n              </div>\n\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>Localisation</h4>\n                  <span>Nouakchott, Mauritanie</span>\n                </div>\n              </div>\n\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fab fa-whatsapp\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>WhatsApp</h4>\n                  <a href=\"http://wa.me/+22238493149\" target=\"_blank\" rel=\"noopener\">\n                    Envoyer un message\n                  </a>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"social-links\">\n              <a href=\"https://github.com/tourad\" target=\"_blank\" rel=\"noopener\" title=\"GitHub\">\n                <i class=\"fa-brands fa-github\"></i>\n              </a>\n              <a href=\"#\" title=\"Facebook\">\n                <i class=\"fa-brands fa-square-facebook\"></i>\n              </a>\n              <a href=\"#\" target=\"_blank\" rel=\"noopener\" title=\"Twitter\">\n                <i class=\"fa-brands fa-square-x-twitter\"></i>\n              </a>\n              <a href=\"#\" title=\"LinkedIn\">\n                <i class=\"fa-brands fa-linkedin\"></i>\n              </a>\n            </div>\n          </div>\n\n          <!-- Formulaire de contact -->\n          <div class=\"contact-form-container animate-on-scroll\">\n            <form class=\"contact-form\" (ngSubmit)=\"onSubmit()\" #contactForm=\"ngForm\">\n              <div class=\"form-group\">\n                <label for=\"name\">Nom complet *</label>\n                <input \n                  type=\"text\" \n                  id=\"name\" \n                  name=\"name\"\n                  [(ngModel)]=\"formData.name\"\n                  required\n                  #nameInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"nameInput.invalid && nameInput.touched\"\n                  placeholder=\"Votre nom complet\">\n                <div class=\"error-message\" *ngIf=\"nameInput.invalid && nameInput.touched\">\n                  Le nom est requis\n                </div>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"email\">Email *</label>\n                <input \n                  type=\"email\" \n                  id=\"email\" \n                  name=\"email\"\n                  [(ngModel)]=\"formData.email\"\n                  required\n                  email\n                  #emailInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"emailInput.invalid && emailInput.touched\"\n                  placeholder=\"<EMAIL>\">\n                <div class=\"error-message\" *ngIf=\"emailInput.invalid && emailInput.touched\">\n                  <span *ngIf=\"emailInput.errors?.['required']\">L'email est requis</span>\n                  <span *ngIf=\"emailInput.errors?.['email']\">Format d'email invalide</span>\n                </div>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"subject\">Sujet *</label>\n                <input \n                  type=\"text\" \n                  id=\"subject\" \n                  name=\"subject\"\n                  [(ngModel)]=\"formData.subject\"\n                  required\n                  #subjectInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"subjectInput.invalid && subjectInput.touched\"\n                  placeholder=\"Sujet de votre message\">\n                <div class=\"error-message\" *ngIf=\"subjectInput.invalid && subjectInput.touched\">\n                  Le sujet est requis\n                </div>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"message\">Message *</label>\n                <textarea \n                  id=\"message\" \n                  name=\"message\"\n                  [(ngModel)]=\"formData.message\"\n                  required\n                  #messageInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"messageInput.invalid && messageInput.touched\"\n                  rows=\"6\"\n                  placeholder=\"Votre message...\"></textarea>\n                <div class=\"error-message\" *ngIf=\"messageInput.invalid && messageInput.touched\">\n                  Le message est requis\n                </div>\n              </div>\n\n              <button \n                type=\"submit\" \n                class=\"submit-btn\"\n                [disabled]=\"contactForm.invalid || isSubmitting\">\n                <span *ngIf=\"!isSubmitting\">\n                  <i class=\"fas fa-paper-plane\"></i>\n                  Envoyer le message\n                </span>\n                <span *ngIf=\"isSubmitting\">\n                  <i class=\"fas fa-spinner fa-spin\"></i>\n                  Envoi en cours...\n                </span>\n              </button>\n            </form>\n\n            <div class=\"form-status\" *ngIf=\"submitStatus\">\n              <div class=\"status-message\" [class]=\"submitStatus.type\">\n                <i [class]=\"submitStatus.icon\"></i>\n                {{ submitStatus.message }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  `,\n  styleUrls: ['./contact.component.scss']\n})], ContactComponent);\nexport { ContactComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "ContactComponent", "constructor", "formData", "name", "email", "subject", "message", "isSubmitting", "submitStatus", "onSubmit", "setTimeout", "type", "icon", "__decorate", "selector", "standalone", "imports", "template", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\contact\\contact.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n\ninterface ContactForm {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\n@Component({\n  selector: 'app-contact',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <section id=\"contact\" class=\"contact-section\">\n      <div class=\"container\">\n        <div class=\"section-header animate-on-scroll\">\n          <h2 class=\"section-title\">Contactez-moi</h2>\n          <p class=\"section-subtitle\">N'hésitez pas à me contacter pour discuter de vos projets</p>\n        </div>\n\n        <div class=\"contact-content\">\n          <!-- Informations de contact -->\n          <div class=\"contact-info animate-on-scroll\">\n            <h3 class=\"info-title\">Restons en contact</h3>\n            <p class=\"info-description\">\n              Je suis toujours ouvert aux nouvelles opportunités et collaborations. \n              Contactez-moi pour discuter de vos projets !\n            </p>\n\n            <div class=\"contact-methods\">\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fas fa-envelope\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>Email</h4>\n                  <a href=\"mailto:<EMAIL>\"><EMAIL></a>\n                </div>\n              </div>\n\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fas fa-phone\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>Téléphone</h4>\n                  <a href=\"tel:+22238493149\">+222 38 49 31 49</a>\n                </div>\n              </div>\n\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>Localisation</h4>\n                  <span>Nouakchott, Mauritanie</span>\n                </div>\n              </div>\n\n              <div class=\"contact-method\">\n                <div class=\"method-icon\">\n                  <i class=\"fab fa-whatsapp\"></i>\n                </div>\n                <div class=\"method-info\">\n                  <h4>WhatsApp</h4>\n                  <a href=\"http://wa.me/+22238493149\" target=\"_blank\" rel=\"noopener\">\n                    Envoyer un message\n                  </a>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"social-links\">\n              <a href=\"https://github.com/tourad\" target=\"_blank\" rel=\"noopener\" title=\"GitHub\">\n                <i class=\"fa-brands fa-github\"></i>\n              </a>\n              <a href=\"#\" title=\"Facebook\">\n                <i class=\"fa-brands fa-square-facebook\"></i>\n              </a>\n              <a href=\"#\" target=\"_blank\" rel=\"noopener\" title=\"Twitter\">\n                <i class=\"fa-brands fa-square-x-twitter\"></i>\n              </a>\n              <a href=\"#\" title=\"LinkedIn\">\n                <i class=\"fa-brands fa-linkedin\"></i>\n              </a>\n            </div>\n          </div>\n\n          <!-- Formulaire de contact -->\n          <div class=\"contact-form-container animate-on-scroll\">\n            <form class=\"contact-form\" (ngSubmit)=\"onSubmit()\" #contactForm=\"ngForm\">\n              <div class=\"form-group\">\n                <label for=\"name\">Nom complet *</label>\n                <input \n                  type=\"text\" \n                  id=\"name\" \n                  name=\"name\"\n                  [(ngModel)]=\"formData.name\"\n                  required\n                  #nameInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"nameInput.invalid && nameInput.touched\"\n                  placeholder=\"Votre nom complet\">\n                <div class=\"error-message\" *ngIf=\"nameInput.invalid && nameInput.touched\">\n                  Le nom est requis\n                </div>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"email\">Email *</label>\n                <input \n                  type=\"email\" \n                  id=\"email\" \n                  name=\"email\"\n                  [(ngModel)]=\"formData.email\"\n                  required\n                  email\n                  #emailInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"emailInput.invalid && emailInput.touched\"\n                  placeholder=\"<EMAIL>\">\n                <div class=\"error-message\" *ngIf=\"emailInput.invalid && emailInput.touched\">\n                  <span *ngIf=\"emailInput.errors?.['required']\">L'email est requis</span>\n                  <span *ngIf=\"emailInput.errors?.['email']\">Format d'email invalide</span>\n                </div>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"subject\">Sujet *</label>\n                <input \n                  type=\"text\" \n                  id=\"subject\" \n                  name=\"subject\"\n                  [(ngModel)]=\"formData.subject\"\n                  required\n                  #subjectInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"subjectInput.invalid && subjectInput.touched\"\n                  placeholder=\"Sujet de votre message\">\n                <div class=\"error-message\" *ngIf=\"subjectInput.invalid && subjectInput.touched\">\n                  Le sujet est requis\n                </div>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"message\">Message *</label>\n                <textarea \n                  id=\"message\" \n                  name=\"message\"\n                  [(ngModel)]=\"formData.message\"\n                  required\n                  #messageInput=\"ngModel\"\n                  class=\"form-control\"\n                  [class.error]=\"messageInput.invalid && messageInput.touched\"\n                  rows=\"6\"\n                  placeholder=\"Votre message...\"></textarea>\n                <div class=\"error-message\" *ngIf=\"messageInput.invalid && messageInput.touched\">\n                  Le message est requis\n                </div>\n              </div>\n\n              <button \n                type=\"submit\" \n                class=\"submit-btn\"\n                [disabled]=\"contactForm.invalid || isSubmitting\">\n                <span *ngIf=\"!isSubmitting\">\n                  <i class=\"fas fa-paper-plane\"></i>\n                  Envoyer le message\n                </span>\n                <span *ngIf=\"isSubmitting\">\n                  <i class=\"fas fa-spinner fa-spin\"></i>\n                  Envoi en cours...\n                </span>\n              </button>\n            </form>\n\n            <div class=\"form-status\" *ngIf=\"submitStatus\">\n              <div class=\"status-message\" [class]=\"submitStatus.type\">\n                <i [class]=\"submitStatus.icon\"></i>\n                {{ submitStatus.message }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  `,\n  styleUrls: ['./contact.component.scss']\n})\nexport class ContactComponent {\n  \n  formData: ContactForm = {\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  };\n\n  isSubmitting = false;\n  submitStatus: { type: string; message: string; icon: string } | null = null;\n\n  onSubmit() {\n    if (this.isSubmitting) return;\n\n    this.isSubmitting = true;\n    this.submitStatus = null;\n\n    // Simulation d'envoi (remplacer par vraie logique d'envoi)\n    setTimeout(() => {\n      this.isSubmitting = false;\n      this.submitStatus = {\n        type: 'success',\n        message: 'Message envoyé avec succès ! Je vous répondrai bientôt.',\n        icon: 'fas fa-check-circle'\n      };\n\n      // Reset form\n      this.formData = {\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      };\n\n      // Clear status after 5 seconds\n      setTimeout(() => {\n        this.submitStatus = null;\n      }, 5000);\n    }, 2000);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AA+LrC,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAAtBC,YAAA;IAEL,KAAAC,QAAQ,GAAgB;MACtBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;KACV;IAED,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,YAAY,GAA2D,IAAI;EA+B7E;EA7BEC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACF,YAAY,EAAE;IAEvB,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IAExB;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACH,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,YAAY,GAAG;QAClBG,IAAI,EAAE,SAAS;QACfL,OAAO,EAAE,yDAAyD;QAClEM,IAAI,EAAE;OACP;MAED;MACA,IAAI,CAACV,QAAQ,GAAG;QACdC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE;OACV;MAED;MACAI,UAAU,CAAC,MAAK;QACd,IAAI,CAACF,YAAY,GAAG,IAAI;MAC1B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC;EACV;CACD;AAzCYR,gBAAgB,GAAAa,UAAA,EAtL5BhB,SAAS,CAAC;EACTiB,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAClB,YAAY,EAAEC,WAAW,CAAC;EACpCkB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+KT;EACDC,SAAS,EAAE,CAAC,0BAA0B;CACvC,CAAC,C,EACWlB,gBAAgB,CAyC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}