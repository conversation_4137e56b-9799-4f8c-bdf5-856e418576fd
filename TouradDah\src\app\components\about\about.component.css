.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #333;
}

.about-content {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.colon1, .colon2 {
    flex: 1;
    margin-right: 20px;
}

.colon2 {
    margin-right: 0;
}

h3 {
    font-size: 1.8rem;
    color: #007bff;
}

p {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
}

.specialties-section {
    margin-top: 40px;
}

.specialties-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.specialties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.specialty-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
}

.specialty-icon {
    font-size: 2rem;
    color: #007bff;
    margin-bottom: 10px;
}

.tech-badge {
    display: inline-block;
    background: #007bff;
    color: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    margin: 5px 0;
}