{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class NavbarComponent {\n  constructor() {\n    this.isMenuOpen = false;\n    this.isDarkTheme = false;\n  }\n  ngOnInit() {\n    this.loadTheme();\n  }\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n  closeMenu() {\n    this.isMenuOpen = false;\n  }\n  toggleTheme() {\n    this.isDarkTheme = !this.isDarkTheme;\n    if (this.isDarkTheme) {\n      document.body.setAttribute('data-theme', 'dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.body.removeAttribute('data-theme');\n      localStorage.setItem('theme', 'light');\n    }\n  }\n  loadTheme() {\n    const savedTheme = localStorage.getItem('theme');\n    this.isDarkTheme = savedTheme === 'dark';\n    if (this.isDarkTheme) {\n      document.body.setAttribute('data-theme', 'dark');\n    }\n  }\n  scrollToSection(sectionId) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n    this.closeMenu();\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 27,\n      vars: 10,\n      consts: [[1, \"navbar-container\"], [1, \"navbar\"], [1, \"scroll-progress\"], [\"href\", \"#\", 1, \"logo\", 3, \"click\"], [1, \"menu-list\"], [\"href\", \"#\", 3, \"click\"], [1, \"nav-controls\"], [1, \"theme-toggle\", 3, \"click\"], [1, \"fas\"], [1, \"menu-icon\", 3, \"click\"], [1, \"fa-sharp\", \"fa-solid\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"nav\")(4, \"a\", 3);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_4_listener() {\n            return ctx.scrollToSection(\"home\");\n          });\n          i0.ɵɵtext(5, \"Tourad Dah\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ul\", 4)(7, \"li\")(8, \"a\", 5);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_8_listener() {\n            return ctx.scrollToSection(\"home\");\n          });\n          i0.ɵɵtext(9, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"li\")(11, \"a\", 5);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_11_listener() {\n            return ctx.scrollToSection(\"about\");\n          });\n          i0.ɵɵtext(12, \"\\u00C0 Propos\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"li\")(14, \"a\", 5);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_14_listener() {\n            return ctx.scrollToSection(\"skills\");\n          });\n          i0.ɵɵtext(15, \"Comp\\u00E9tences\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\")(17, \"a\", 5);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_17_listener() {\n            return ctx.scrollToSection(\"portfolio\");\n          });\n          i0.ɵɵtext(18, \"Projets\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"li\")(20, \"a\", 5);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_20_listener() {\n            return ctx.scrollToSection(\"contact\");\n          });\n          i0.ɵɵtext(21, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 6)(23, \"div\", 7);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_div_click_23_listener() {\n            return ctx.toggleTheme();\n          });\n          i0.ɵɵelement(24, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_div_click_25_listener() {\n            return ctx.toggleMenu();\n          });\n          i0.ɵɵelement(26, \"i\", 10);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"show\", ctx.isMenuOpen);\n          i0.ɵɵadvance(18);\n          i0.ɵɵclassProp(\"fa-sun\", !ctx.isDarkTheme)(\"fa-moon\", ctx.isDarkTheme);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"fa-bars\", !ctx.isMenuOpen)(\"fa-times\", ctx.isMenuOpen);\n        }\n      },\n      dependencies: [CommonModule, RouterModule],\n      styles: [\".navbar-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background: var(--navbar-bg);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom: 1px solid var(--border-color);\\n  transition: all 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.scroll-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  height: 3px;\\n  background: var(--gradient-primary);\\n  transition: width 0.3s ease;\\n  z-index: 1001;\\n}\\n\\nnav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1rem 2rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: var(--primary-color);\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.logo[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.menu-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  gap: 2rem;\\n  margin: 0;\\n  padding: 0;\\n}\\n.menu-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--text-color);\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.menu-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n.menu-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background: var(--primary-color);\\n  transition: width 0.3s ease;\\n}\\n.menu-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.nav-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.theme-toggle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.theme-toggle[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.theme-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.menu-icon[_ngcontent-%COMP%] {\\n  display: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  background: var(--card-bg);\\n  border: 1px solid var(--border-color);\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.menu-icon[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n.menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n\\n@media (max-width: 768px) {\\n  .menu-list[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: 100%;\\n    left: 0;\\n    right: 0;\\n    flex-direction: column;\\n    background: var(--navbar-bg);\\n    border: 1px solid var(--border-color);\\n    border-top: none;\\n    padding: 1rem;\\n    gap: 1rem;\\n    transform: translateY(-100%);\\n    opacity: 0;\\n    visibility: hidden;\\n    transition: all 0.3s ease;\\n  }\\n  .menu-list.show[_ngcontent-%COMP%] {\\n    transform: translateY(0);\\n    opacity: 1;\\n    visibility: visible;\\n  }\\n  .menu-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    display: block;\\n    padding: 0.5rem 0;\\n    border-bottom: 1px solid var(--border-color);\\n  }\\n  .menu-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:last-child {\\n    border-bottom: none;\\n  }\\n  .menu-icon[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  nav[_ngcontent-%COMP%] {\\n    padding: 0.8rem 1rem;\\n  }\\n  .logo[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  nav[_ngcontent-%COMP%] {\\n    padding: 0.6rem 1rem;\\n  }\\n  .logo[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .theme-toggle[_ngcontent-%COMP%], .menu-icon[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .theme-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "NavbarComponent", "constructor", "isMenuOpen", "isDarkTheme", "ngOnInit", "loadTheme", "toggleMenu", "closeMenu", "toggleTheme", "document", "body", "setAttribute", "localStorage", "setItem", "removeAttribute", "savedTheme", "getItem", "scrollToSection", "sectionId", "element", "getElementById", "offsetTop", "window", "scrollTo", "top", "behavior", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵlistener", "NavbarComponent_Template_a_click_4_listener", "ɵɵtext", "ɵɵelementEnd", "NavbarComponent_Template_a_click_8_listener", "NavbarComponent_Template_a_click_11_listener", "NavbarComponent_Template_a_click_14_listener", "NavbarComponent_Template_a_click_17_listener", "NavbarComponent_Template_a_click_20_listener", "NavbarComponent_Template_div_click_23_listener", "NavbarComponent_Template_div_click_25_listener", "ɵɵadvance", "ɵɵclassProp", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\navbar\\navbar.component.ts", "C:\\Users\\<USER>\\Desktop\\Mywebsite\\TouradDah\\src\\app\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-navbar',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.scss']\n})\nexport class NavbarComponent implements OnInit {\n  isMenuOpen = false;\n  isDarkTheme = false;\n\n  ngOnInit() {\n    this.loadTheme();\n  }\n\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n\n  closeMenu() {\n    this.isMenuOpen = false;\n  }\n\n  toggleTheme() {\n    this.isDarkTheme = !this.isDarkTheme;\n    \n    if (this.isDarkTheme) {\n      document.body.setAttribute('data-theme', 'dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.body.removeAttribute('data-theme');\n      localStorage.setItem('theme', 'light');\n    }\n  }\n\n  private loadTheme() {\n    const savedTheme = localStorage.getItem('theme');\n    this.isDarkTheme = savedTheme === 'dark';\n    \n    if (this.isDarkTheme) {\n      document.body.setAttribute('data-theme', 'dark');\n    }\n  }\n\n  scrollToSection(sectionId: string) {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80;\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n    this.closeMenu();\n  }\n}\n", "<header class=\"navbar-container\">\n  <div class=\"navbar\">\n    <div class=\"scroll-progress\"></div>\n    <nav>\n      <a href=\"#\" class=\"logo\" (click)=\"scrollToSection('home')\"><PERSON><PERSON> Dah</a>\n      \n      <ul class=\"menu-list\" [class.show]=\"isMenuOpen\">\n        <li><a href=\"#\" (click)=\"scrollToSection('home')\">Accueil</a></li>\n        <li><a href=\"#\" (click)=\"scrollToSection('about')\">À Propos</a></li>\n        <li><a href=\"#\" (click)=\"scrollToSection('skills')\">Compétences</a></li>\n        <li><a href=\"#\" (click)=\"scrollToSection('portfolio')\">Projets</a></li>\n        <li><a href=\"#\" (click)=\"scrollToSection('contact')\">Contact</a></li>\n      </ul>\n      \n      <div class=\"nav-controls\">\n        <div class=\"theme-toggle\" (click)=\"toggleTheme()\">\n          <i class=\"fas\" [class.fa-sun]=\"!isDarkTheme\" [class.fa-moon]=\"isDarkTheme\"></i>\n        </div>\n        <div class=\"menu-icon\" (click)=\"toggleMenu()\">\n          <i class=\"fa-sharp fa-solid\" [class.fa-bars]=\"!isMenuOpen\" [class.fa-times]=\"isMenuOpen\"></i>\n        </div>\n      </div>\n    </nav>\n  </div>\n</header>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;AAS9C,OAAM,MAAOC,eAAe;EAP5BC,YAAA;IAQE,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,KAAK;;EAEnBC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACJ,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAK,SAASA,CAAA;IACP,IAAI,CAACL,UAAU,GAAG,KAAK;EACzB;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACL,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAACA,WAAW,EAAE;MACpBM,QAAQ,CAACC,IAAI,CAACC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;MAChDC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;KACtC,MAAM;MACLJ,QAAQ,CAACC,IAAI,CAACI,eAAe,CAAC,YAAY,CAAC;MAC3CF,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;;EAE1C;EAEQR,SAASA,CAAA;IACf,MAAMU,UAAU,GAAGH,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;IAChD,IAAI,CAACb,WAAW,GAAGY,UAAU,KAAK,MAAM;IAExC,IAAI,IAAI,CAACZ,WAAW,EAAE;MACpBM,QAAQ,CAACC,IAAI,CAACC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;;EAEpD;EAEAM,eAAeA,CAACC,SAAiB;IAC/B,MAAMC,OAAO,GAAGV,QAAQ,CAACW,cAAc,CAACF,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACX,MAAME,SAAS,GAAGF,OAAO,CAACE,SAAS,GAAG,EAAE;MACxCC,MAAM,CAACC,QAAQ,CAAC;QACdC,GAAG,EAAEH,SAAS;QACdI,QAAQ,EAAE;OACX,CAAC;;IAEJ,IAAI,CAAClB,SAAS,EAAE;EAClB;;;uBA/CWP,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAA0B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV1BP,EADF,CAAAS,cAAA,gBAAiC,aACX;UAClBT,EAAA,CAAAU,SAAA,aAAmC;UAEjCV,EADF,CAAAS,cAAA,UAAK,WACwD;UAAlCT,EAAA,CAAAW,UAAA,mBAAAC,4CAAA;YAAA,OAASJ,GAAA,CAAApB,eAAA,CAAgB,MAAM,CAAC;UAAA,EAAC;UAACY,EAAA,CAAAa,MAAA,iBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAGnEd,EADN,CAAAS,cAAA,YAAgD,SAC1C,WAA8C;UAAlCT,EAAA,CAAAW,UAAA,mBAAAI,4CAAA;YAAA,OAASP,GAAA,CAAApB,eAAA,CAAgB,MAAM,CAAC;UAAA,EAAC;UAACY,EAAA,CAAAa,MAAA,cAAO;UAAIb,EAAJ,CAAAc,YAAA,EAAI,EAAK;UAC9Dd,EAAJ,CAAAS,cAAA,UAAI,YAA+C;UAAnCT,EAAA,CAAAW,UAAA,mBAAAK,6CAAA;YAAA,OAASR,GAAA,CAAApB,eAAA,CAAgB,OAAO,CAAC;UAAA,EAAC;UAACY,EAAA,CAAAa,MAAA,qBAAQ;UAAIb,EAAJ,CAAAc,YAAA,EAAI,EAAK;UAChEd,EAAJ,CAAAS,cAAA,UAAI,YAAgD;UAApCT,EAAA,CAAAW,UAAA,mBAAAM,6CAAA;YAAA,OAAST,GAAA,CAAApB,eAAA,CAAgB,QAAQ,CAAC;UAAA,EAAC;UAACY,EAAA,CAAAa,MAAA,wBAAW;UAAIb,EAAJ,CAAAc,YAAA,EAAI,EAAK;UACpEd,EAAJ,CAAAS,cAAA,UAAI,YAAmD;UAAvCT,EAAA,CAAAW,UAAA,mBAAAO,6CAAA;YAAA,OAASV,GAAA,CAAApB,eAAA,CAAgB,WAAW,CAAC;UAAA,EAAC;UAACY,EAAA,CAAAa,MAAA,eAAO;UAAIb,EAAJ,CAAAc,YAAA,EAAI,EAAK;UACnEd,EAAJ,CAAAS,cAAA,UAAI,YAAiD;UAArCT,EAAA,CAAAW,UAAA,mBAAAQ,6CAAA;YAAA,OAASX,GAAA,CAAApB,eAAA,CAAgB,SAAS,CAAC;UAAA,EAAC;UAACY,EAAA,CAAAa,MAAA,eAAO;UAC9Db,EAD8D,CAAAc,YAAA,EAAI,EAAK,EAClE;UAGHd,EADF,CAAAS,cAAA,cAA0B,cAC0B;UAAxBT,EAAA,CAAAW,UAAA,mBAAAS,+CAAA;YAAA,OAASZ,GAAA,CAAA7B,WAAA,EAAa;UAAA,EAAC;UAC/CqB,EAAA,CAAAU,SAAA,YAA+E;UACjFV,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAS,cAAA,cAA8C;UAAvBT,EAAA,CAAAW,UAAA,mBAAAU,+CAAA;YAAA,OAASb,GAAA,CAAA/B,UAAA,EAAY;UAAA,EAAC;UAC3CuB,EAAA,CAAAU,SAAA,aAA6F;UAKvGV,EAJQ,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACC;;;UAlBmBd,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAuB,WAAA,SAAAf,GAAA,CAAAnC,UAAA,CAAyB;UAU5B2B,EAAA,CAAAsB,SAAA,IAA6B;UAACtB,EAA9B,CAAAuB,WAAA,YAAAf,GAAA,CAAAlC,WAAA,CAA6B,YAAAkC,GAAA,CAAAlC,WAAA,CAA8B;UAG7C0B,EAAA,CAAAsB,SAAA,GAA6B;UAACtB,EAA9B,CAAAuB,WAAA,aAAAf,GAAA,CAAAnC,UAAA,CAA6B,aAAAmC,GAAA,CAAAnC,UAAA,CAA8B;;;qBDZtFJ,YAAY,EAAEC,YAAY;MAAAsD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}