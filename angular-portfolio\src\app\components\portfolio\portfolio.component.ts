export class PortfolioComponent {
  projects = [
    {
      title: 'Portfolio Personnel',
      description: 'Site web personnel moderne et responsive présentant mes compétences et projets.',
      image: 'assets/img/portfolio-5.png',
      year: 2024,
      duration: '2 semaines',
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'Responsive'],
      status: 'Terminé',
      category: 'web',
      links: {
        view: '#',
        code: '#',
        demo: '#'
      }
    },
    {
      title: 'Système de Gestion de Bibliothèque',
      description: 'Application web complète pour la gestion des livres, emprunts et utilisateurs d\'une bibliothèque.',
      image: 'assets/img/portfolio-1.png',
      year: 2024,
      duration: '1 mois',
      technologies: ['Django', 'Python', 'Bootstrap', 'SQLite', 'JavaScript'],
      status: 'Terminé',
      category: 'fullstack',
      links: {
        view: '#',
        code: 'https://github.com/devTourad/Gestion_Bibliotheque',
        demo: '#'
      }
    },
    {
      title: 'Système de Facturation',
      description: 'Application de gestion de facturation avec génération automatique de factures et suivi des paiements.',
      image: 'assets/img/2.png',
      year: 2024,
      duration: '3 semaines',
      technologies: ['Laravel', 'Blade', 'HTML', 'CSS3', 'Bootstrap', 'JavaScript'],
      status: 'Terminé',
      category: 'fullstack',
      links: {
        view: '#',
        code: 'https://github.com/devTourad/SYSTEM-FACTURATION',
        demo: '#'
      }
    },
    {
      title: 'Système de Compte Bancaire',
      description: 'Application de Compte Bancaire.',
      image: 'assets/img/github.png',
      year: 2024,
      duration: '3 semaines',
      technologies: ['JAVA', 'AWT', 'Mysql', 'Eclipse'],
      status: 'Terminé',
      category: 'fullstack',
      links: {
        view: '#',
        code: 'https://github.com/devTourad/',
        demo: '#'
      }
    },
    {
      title: 'Système Intelligent d\'Éclairage Public',
      description: 'Conception d\'un système intelligent pour optimiser la gestion de l\'éclairage public urbain.',
      image: 'assets/img/portfolio-4.jpg',
      year: 2024,
      duration: '2 mois',
      technologies: ['IoT', 'Arduino', 'Python', 'Web Dashboard', 'Capteurs'],
      status: 'Terminé',
      category: 'web',
      links: {
        view: '#',
        documentation: '#',
        presentation: '#'
      }
    }
  ];

  filter = 'all';

  get filteredProjects() {
    if (this.filter === 'all') {
      return this.projects;
    }
    return this.projects.filter(project => project.category === this.filter);
  }

  setFilter(filter: string) {
    this.filter = filter;
  }
}