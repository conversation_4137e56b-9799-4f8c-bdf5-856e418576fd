<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Tourad Dah - Développeur Full-stack | Portfolio</title>
    <meta name="description" content="Portfolio de Tourad Med Mahmoud Dah, développeur full-stack spécialisé en cybersécurité et développement web moderne. Découvrez mes projets et compétences.">
    <meta name="keywords" content="développeur, full-stack, cybersécurité, web development, portfolio, Tourad Dah">
    <meta name="author" content="Tourad Med Mahmoud Dah">
    <meta property="og:title" conclstent="Tourad Dah - Développeur Full-stack">
    <meta property="og:description" content="Portfolio de Tourad Med <PERSON>, développeur full-stack spécialisé en cybersécurité">
    <meta property="og:type" content="website">
    <meta property="og:image" content="./img/Tourad1.jpg">
    <meta name="twitter:card" content="summary_large_image">
    <link rel="stylesheet" href="index.css">
    <link rel="shortcut icon" href="./img/favicon.ico" type="image/x-icon">
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" 
     integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" 
     crossorigin="anonymous" referrerpolicy="no-referrer" />


     <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.5/css/boxicons.min.css' rel='stylesheet'>
</head>
<body>
    <header id="home">
        <div class="navbar">
            <div class="scroll-progress"></div>
            <nav>
                <a href="#home" id="logo" >Tourad Dah </a>
                <ul id="menuList">
                    <li><a href="#home">Accueil</a></li>
                    <li><a href="#about">À Propos</a></li>
                    <li><a href="#skills">Compétences</a></li>
                    <li><a href="#security">Sécurité</a></li>
                    <li><a href="#portfolio">Projets</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <div class="nav-controls">
                    <div class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-sun" id="theme-icon"></i>
                    </div>
                    <div class="menu-icon">
                        <i class="fa-sharp fa-solid fa-bars" onclick="toggleMenu()"></i>
                    </div>
                </div>
            </nav>
        </div>
   <div class="header-content">
       <div class="content-text">
        <div class="greeting-section">
            <p class="greeting"><span class="text-pimary">Salut, je suis</span></p>
            <h1 id="typewriter-name">Tourad Med Mahmoud Dah</h1>
            <p class="title" id="typewriter-title">Développeur Full-stack</p>
            <p class="subtitle">Spécialisé en cybersécurité et développement web moderne</p>
        </div>



        <div class="skills-highlight">
            <!-- <div class="skill-badge">
                <i class="fas fa-shield-alt"></i>
                <span>Cybersécurité</span>
            </div> -->
            <div class="skill-badge">
                <i class="fas fa-code"></i>
                <span>Full-stack</span>
            </div>
            <div class="skill-badge">
                <i class="fas fa-bug"></i>
                <span>Bug Bounty</span>
            </div>
        </div>

        <div class="social">
            <a href="https://github.com/tourad" target="_blank" rel="noopener" title="GitHub"><i class="fa-brands fa-github"></i></a>
            <a href="#" title="Facebook"><i class="fa-brands fa-square-facebook"></i></a>
            <a href="#" target="_blank" rel="noopener" title="Twitter"><i class="fa-brands fa-square-x-twitter"></i></a>
            <a href="#" title="LinkedIn"><i class="fa-brands fa-linkedin"></i></a>
            <a href="http://wa.me/+22238493149" target="_blank" rel="noopener" title="WhatsApp"><i class="fa-brands fa-whatsapp"></i></a>
        </div>

        <div class="cta-buttons">
            <a href="#portfolio" class="btn-primary enhanced">
                <span class="btn-text">Découvrir mes projets</span>
                <span class="btn-icon">
                    <i class="fa-solid fa-rocket"></i>
                </span>
                <div class="btn-shine"></div>
            </a>
        </div>
       </div>
       <div class="home__img">
        <div class="image-container">
            <img src="img/Tourad1.jpg" alt="Tourad Med Mahmoud Dah - Développeur Full-stack">
            <div class="image-overlay">
                <div class="floating-badge">
                    <i class="fas fa-code"></i>
                    <span>Développeur</span>
                </div>
            </div>
        </div>
       </div>
</header>

<main>
    <section id="about" class="animate-on-scroll">
         <div class="container">
            <h2>À Propos de Moi</h2>
            <hr>
            <div class="about-content">
                <div class="colon1">
                    <h3>Je suis <span class="text-pimary">Tourad Med Mahmoud Dah,</span> développeur full-stack</h3>
                    <p>
                        Étudiant passionné à l'Institut Supérieur de Comptabilité et de Gestion Institutionnelle,
                        spécialisé en Management Informatique & Technologie (IT). Mon parcours académique me permet
                        d'allier compétences techniques et vision stratégique des systèmes d'information.
                    </p>
                    <p>
                       
                    </p>
                    <p>
                        <strong>Passionné de cybersécurité</strong>, j'étudie activement le bug bounty et les
                        techniques de sécurité offensive. Je me spécialise dans la détection de vulnérabilités
                        web, notamment les <strong>injections SQL</strong>, XSS, CSRF et autres failles OWASP.
                        Cette expertise en sécurité enrichit considérablement mes compétences en développement
                        sécurisé.
                    </p>
                    <p>
                        Mon approche combine développement robuste et sécurité proactive, me permettant de créer
                        des applications performantes et sécurisées. Je reste constamment à jour avec les dernières
                        technologies et pratiques de sécurité du secteur.
                    </p>

                </div>
                <div class="colon2">
                    <p><b>Age : </b>19 ans</p>
                    <p><b>Email : </b><EMAIL></p>
                    <p><b>Localisation : </b>Nouakchott, Mauritanie</p>
                    <p><b>Spécialité : </b>Développement </p>
                </div>
            </div>

            <!-- Mes Spécialités -->
            <div class="specialties-section animate-on-scroll">
                <h3 class="specialties-title">Mes Spécialités</h3>
                <div class="specialties-grid">
                    
                    <div class="specialty-card">
                        <div class="specialty-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h4>Applications Web</h4>
                        <p>Création de sites web dynamiques et responsifs avec les technologies modernes.</p>
                    </div>
                    <!-- <div class="specialty-card">
                        <div class="specialty-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>Applications Mobiles</h4>
                        <p>Développement d'applications mobiles natives et hybrides .</p>
                    </div> -->
                    <div class="specialty-card">
                        <div class="specialty-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h4>Fullstack Development</h4>
                        <p>Maîtrise des technologies frontend et backend pour développer des applications de bout en bout.</p>
                    </div>
                </div>
            </div>
         </div>
    </section>

   <!--bidayit security section-->
    <section id="security" class="security-section animate-on-scroll">
        <div class="container">
            <h2><i class="fas fa-shield-alt"></i> Sécurité Web <span class="text-pimary">Spécialisée</span></h2>
            <hr>

            <div class="security-intro animate-on-scroll">
                <p class="security-description">
                    Spécialiste en cybersécurité avec une expertise approfondie dans la sécurisation des applications web,
                    l'analyse des vulnérabilités et la mise en place de mesures de protection avancées.
                </p>
            </div>

            <div class="security-content animate-on-scroll">

                <!-- Sécurité des Applications Web -->
                <div class="security-category">
                    <h3 class="security-title"><i class="fas fa-globe-americas"></i> Sécurité des Applications Web</h3>
                    <div class="security-grid">
                        <div class="security-item">
                            <div class="security-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <h4>Authentification & Autorisation</h4>
                            <p>JWT, OAuth 2.0, Session Management, Multi-Factor Authentication</p>
                            <div class="security-level">
                                <span class="level-badge expert">Expert</span>
                            </div>
                        </div>

                        <div class="security-item">
                            <div class="security-icon">
                                <i class="fas fa-bug"></i>
                            </div>
                            <h4>Tests de Pénétration</h4>
                            <p>OWASP Top 10, Injection SQL, XSS, CSRF, Tests d'intrusion</p>
                            <div class="security-level">
                                <span class="level-badge advanced">Avancé</span>
                            </div>
                        </div>

                        <!-- <div class="security-item">
                            <div class="security-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <h4>Cryptographie</h4>
                            <p>Chiffrement AES, RSA, Hachage sécurisé, Certificats SSL/TLS</p>
                            <div class="security-level">
                                <span class="level-badge expert">Expert</span>
                            </div>
                        </div> -->
                    </div>
                </div>

                <!-- Outils de Sécurité -->
                <div class="security-category">
                    <h3 class="security-title"><i class="fas fa-tools"></i> Outils de Sécurité</h3>
                    <div class="security-tools">
                        <div class="tool-item">
                            <div class="tool-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="tool-info">
                                <h5>Burp Suite</h5>
                                <p>Analyse de vulnérabilités web</p>
                                <div class="tool-progress">
                                    <div class="progress-bar" data-percentage="85"></div>
                                    <span class="percentage">85%</span>
                                </div>
                            </div>
                        </div>

                        <div class="tool-item">
                            <div class="tool-icon">
                                <i class="fas fa-terminal"></i>
                            </div>
                            <div class="tool-info">
                                <h5>Nmap & Nessus</h5>
                                <p>Scan de ports et vulnérabilités</p>
                                <div class="tool-progress">
                                    <div class="progress-bar" data-percentage="80"></div>
                                    <span class="percentage">80%</span>
                                </div>
                            </div>
                        </div>

                        <div class="tool-item">
                            <div class="tool-icon">
                                <i class="fas fa-shield-virus"></i>
                            </div>
                            <div class="tool-info">
                                <h5>OWASP ZAP</h5>
                                <p>Proxy de sécurité pour applications web</p>
                                <div class="tool-progress">
                                    <div class="progress-bar" data-percentage="75"></div>
                                    <span class="percentage">75%</span>
                                </div>
                            </div>
                        </div>

                        <div class="tool-item">
                            <div class="tool-icon">
                                <i class="fab fa-linux"></i>
                            </div>
                            <div class="tool-info">
                                <h5>Kali Linux</h5>
                                <p>Distribution pour tests de sécurité</p>
                                <div class="tool-progress">
                                    <div class="progress-bar" data-percentage="90"></div>
                                    <span class="percentage">90%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services de Sécurité -->
                <div class="security-category">
                    <h3 class="security-title"><i class="fas fa-cogs"></i> Services de Sécurité</h3>
                    <div class="services-list">
                        <div class="service-item">
                            <div class="service-header">
                                <i class="fas fa-search-plus"></i>
                                <h4>Audit de Sécurité</h4>
                            </div>
                            <ul class="service-details">
                                <li>Analyse complète des vulnérabilités</li>
                                <li>Tests de pénétration applicatifs</li>
                                <li>Rapport détaillé avec recommandations</li>
                                <li>Plan de remédiation prioritisé</li>
                            </ul>
                        </div>

                        <div class="service-item">
                            <div class="service-header">
                                <i class="fas fa-code"></i>
                                <h4>Développement Sécurisé</h4>
                            </div>
                            <ul class="service-details">
                                <li>Code review orienté sécurité</li>
                                <li>Implémentation de bonnes pratiques</li>
                                <li>Intégration de tests de sécurité</li>
                                <li>Formation des équipes de développement</li>
                            </ul>
                        </div>

                      
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--nihayit security section-->

    <!--skill-->
    <section id="skills" class="animate-on-scroll">
        <div class="container">
            <h2><i class="fa-solid fa-laptop-code"></i> Compétences  <span class="text-pimary"></span></h2>
            <hr>
            <div class="skills-content animate-on-scroll">

                <!-- Frontend -->
                <div class="skill-category">
                    <h3 class="category-title"><i class="fa-solid fa-desktop"></i> Frontend</h3>
                    <div class="row">
                        <div class="item">
                            <img src="img/html.png" alt="Html">
                            <p>HTML</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="90"></div>
                                <span class="percentage">90%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/css.png" alt="css">
                            <p>CSS</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="85"></div>
                                <span class="percentage">85%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/js.png" alt="javascript">
                            <p>JS</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="80"></div>
                                <span class="percentage">80%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/nextjs.png" alt="nextjs">
                            <p>Next.js</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="75"></div>
                                <span class="percentage">75%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/angular.png" alt="angularjs">
                            <p>Angular</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="50"></div>
                                <span class="percentage">50%</span>
                            </div>
                        </div>
                          <div class="item">
                            <img src="img/botstrap.png" alt="bootstrap">
                            <p>Bootstrap</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="85"></div>
                                <span class="percentage">85%</span>
                            </div>
                        </div>
                       
                    </div>
                </div>

                <!-- Backend -->
                <div class="skill-category">
                    <h3 class="category-title"><i class="fa-solid fa-code"></i> Backend</h3>
                    <div class="row">
                         <div class="item">
                            <img src="img/d.png" alt="django">
                            <p>Django</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="80"></div>
                                <span class="percentage">80%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/laravel.png" alt="laravel">
                            <p>Laravel</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="75"></div>
                                <span class="percentage">75%</span>
                            </div>
                        </div>
                        
                        <div class="item">
                            <img src="img/php.png" alt="php">
                            <p>PHP</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="80"></div>
                                <span class="percentage">80%</span>
                            </div>
                        </div>
                         
                        <div class="item">
                            <img src="img/java.png" alt="java">
                            <p>Java</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="70"></div>
                                <span class="percentage">70%</span>
                            </div>
                        </div>
                       
                    </div>
                </div>

                <!-- Stack & Frameworks -->
                <div class="skill-category">
                    <h3 class="category-title"><i class="fas fa-code-branch"></i>Stack & Frameworks</h3>
                    <div class="row stack-row">
                  
                        <div class="item stack-item">
                            <div class="stack-header">
                                <h4>Django & Next.js</h4>
                            </div>
                            <div class="stack-technologies">
                                <span class="tech-badge">Django</span>
                                <span class="tech-badge">Next.js</span>
                                <span class="tech-badge">PostgreSQL</span>
                            </div>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="90"></div>
                                <span class="percentage">90%</span>
                            </div>
                        </div>
                          <div class="item stack-item">
                            <div class="stack-header">
                                <h4>Laravel & Angular.js</h4>
                            </div>
                            <div class="stack-technologies">
                                <span class="tech-badge">Laravel</span>
                                <span class="tech-badge">Angular.js</span>
                                <span class="tech-badge">Mysql</span>
                            </div>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="60"></div>
                                <span class="percentage">60%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bases de Données -->
                <div class="skill-category">
                    <h3 class="category-title"><i class="fas fa-database"></i> Bases de Données</h3>
                    <div class="row">
                        <div class="item">
                            <img src="img/mysql.png" alt="mysql">
                            <p>MySQL</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="80"></div>
                                <span class="percentage">80%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/postgre.png" alt="postgresql">
                            <p>PostgreSQL</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="75"></div>
                                <span class="percentage">75%</span>
                            </div>
                        </div>
                          <div class="item">
                            <img src="img/sqlite.png" alt="sqlite">
                            <p>Sqlite</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="99"></div>
                                <span class="percentage">99%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Outils -->
                <div class="skill-category">
                    <h3 class="category-title"><i class="fas fa-tools"></i> Outils</h3>
                    <div class="row">
                    
                        <div class="item">
                            <img src="img/vscode.png" alt="vscode">
                            <p>VS Code</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="90"></div>
                                <span class="percentage">90%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/git.png" alt="git">
                            <p>Git</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="80"></div>
                                <span class="percentage">80%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/github.png" alt="github">
                            <p>GitHub</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="85"></div>
                                <span class="percentage">85%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/docker.png" alt="docker">
                            <p>Docker</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="70"></div>
                                <span class="percentage">70%</span>
                            </div>
                        </div>
                        <div class="item">
                            <img src="img/eclipse.png" alt="eclipse">
                            <p>Eclipse</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="75"></div>
                                <span class="percentage">75%</span>
                            </div>
                        </div>
                            <div class="item">
                            <img src="img/figma.png" alt="figma">
                            <p>Figma</p>
                            <div class="skill-progress">
                                <div class="progress-bar" data-percentage="85"></div>
                                <span class="percentage">65%</span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </section>
    <!--nihayit skill-->

 

    <!--bidayit portfolio-->
    <section id="portfolio" class="animate-on-scroll">
        <div class="container">
            <h2><i class="fa-solid fa-laptop-code"></i> Mes <span class="text-pimary">Projets</span></h2>
            <hr>
            <p class="portfolio-description">Découvrez une sélection de mes projets récents qui démontrent mes compétences en développement web et mobile.</p>

            <!-- Filtres de projets -->
            <div class="portfolio-filters">
                <button type="button" class="filter-btn active" data-filter="all">Tous</button>
                <button type="button" class="filter-btn" data-filter="web">Web</button>
                <button type="button" class="filter-btn" data-filter="mobile">Mobile</button>
                <button type="button" class="filter-btn" data-filter="fullstack">Fullstack</button>
            </div>

            <div class="portfolio-grid animate-on-scroll">
<!--                 
                <div class="portfolio-item" data-category="web">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="img/portfolio-5.png" alt="Portfolio Website">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="#" class="action-btn" title="Voir le projet">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="#" class="action-btn" title="Code source">
                                        <i class="fab fa-github"></i>
                                    </a>
                                    <a href="#" class="action-btn" title="Site web">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <div class="portfolio-header">
                                <h3>Portfolio Personnel</h3>
                                <span class="project-status completed">Terminé</span>
                            </div>
                            <p class="portfolio-description-text">Site web personnel moderne et responsive présentant mes compétences et projets.</p>
                            <div class="portfolio-tech">
                                <span class="tech-tag">HTML5</span>
                                <span class="tech-tag">CSS3</span>
                                <span class="tech-tag">JavaScript</span>
                                <span class="tech-tag">Responsive</span>
                            </div>
                            <div class="portfolio-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>2024</span>
                                </div>

                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- Projet 2: Gestion Bibliothèque -->
                <div class="portfolio-item" data-category="fullstack">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="img/portfolio-1.png" alt="Gestion Bibliothèque">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="#" class="action-btn" title="Voir le projet">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="https://github.com/devTourad/Gestion_Bibliotheque" class="action-btn" title="Code source">
                                        <i class="fab fa-github"></i>
                                    </a>
                                    <a href="#" class="action-btn" title="Démo">
                                        <i class="fas fa-play"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <div class="portfolio-header">
                                <h3>Système de Gestion de Bibliothèque</h3>
                                <span class="project-status completed">Terminé</span>
                            </div>
                            <p class="portfolio-description-text">Application web complète pour la gestion des livres, emprunts et utilisateurs d'une bibliothèque.</p>
                            <div class="portfolio-tech">
                                <span class="tech-tag">Django</span>
                                <span class="tech-tag">Python</span>
                                <span class="tech-tag">Bootstrap</span>
                                <span class="tech-tag">SQLite</span>
                                <span class="tech-tag">JavaScript</span>
                            </div>
                            <div class="portfolio-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>2024</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Projet 3: Système de Facturation -->
                <div class="portfolio-item" data-category="fullstack">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="img/2.png" alt="Système de Facturation">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="#" class="action-btn" title="Voir le projet">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="https://github.com/devTourad/SYSTEM-FACTURATION" class="action-btn" title="Code source">
                                        <i class="fab fa-github"></i>
                                    </a>
                                    <a href="#" class="action-btn" title="Démo">
                                        <i class="fas fa-play"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <div class="portfolio-header">
                                <h3>Système de Facturation</h3>
                                <span class="project-status completed">Terminé</span>
                            </div>
                            <p class="portfolio-description-text">Application de gestion de facturation avec génération automatique de factures et suivi des paiements.</p>
                            <div class="portfolio-tech">
                                <span class="tech-tag">Laravel</span>
                                <span class="tech-tag">Blade</span>
                                <span class="tech-tag">HTML</span>
                                <span class="tech-tag">CSS3</span>
                                <span class="tech-tag">Bootstrap</span>
                                <span class="tech-tag">JavaScript</span>
                            </div>
                            <div class="portfolio-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>2024</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


   <div class="portfolio-item" data-category="fullstack">
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <img src="img/github.png" alt="Système de Compte Bancaire">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <a href="#" class="action-btn" title="Voir le projet">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="https://github.com/devTourad/" class="action-btn" title="Code source">
                                        <i class="fab fa-github"></i>
                                    </a>
                                    <a href="#" class="action-btn" title="Démo">
                                        <i class="fas fa-play"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-content">
                            <div class="portfolio-header">
                                <h3>Système de Compte Bancaire</h3>
                                <span class="project-status completed">Terminé</span>
                            </div>
                            <p class="portfolio-description-text">Application  de Compte Bancaire.</p>
                            <div class="portfolio-tech">
                                <span class="tech-tag">JAVA</span>
                                <span class="tech-tag">AWT</span>
                                <span class="tech-tag">Mysql</span>
                                <span class="tech-tag">Eclipse</span>
                            </div>
                            <div class="portfolio-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>2024</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                
            </div>
        </div>
    </section>
    <!--bidayit edu-->
    <section id="Education" class="animate-on-scroll">
        <div class="container">
            <h2><i class="fas fa-graduation-cap"></i> Mon <span class="text-pimary">Parcours</span></h2>
            <hr>

            <!-- Timeline Education -->
            <div class="education-timeline">
                <div class="timeline-container">

                    <!-- Formation Actuelle -->
                    <div class="timeline-item animate-on-scroll" data-year="2023-2026">
                        <div class="timeline-dot current">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="education-card current">
                                <div class="card-header">
                                    <div class="institution-logo">
                                        <img src="img/logo-iscae.jpg" alt="ISCAE" class="college-image">
                                    </div>
                                    <div class="card-badge current-badge">
                                        <i class="fas fa-clock"></i>
                                        En cours
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h3 class="degree">Bachelor of Science</h3>
                                    <h4 class="specialization">Management Informatique & Technologie (IT)</h4>
                                    <p class="institution">Higher Institute of Accounting and Institutional Management</p>
                                    <div class="education-details">
                                        <div class="detail-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <span>2023 - 2026</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>Nouakchott, Mauritanie</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-star"></i>
                                            <span>Spécialisation IT</span>
                                        </div>
                                    </div>
                                    <div class="skills-acquired">
                                        <h5>Compétences acquises :</h5>
                                        <div class="skill-tags">
                                            <span class="skill-tag">Gestion de projet</span>
                                            <span class="skill-tag">Développement web</span>
                                            <span class="skill-tag">Base de données</span>
                                            <span class="skill-tag">Systèmes d'information</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stage Professionnel -->
                    <div class="timeline-item animate-on-scroll" data-year="2024">
                        <div class="timeline-dot internship">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="education-card internship">
                                <div class="card-header">
                                    <div class="institution-logo">
                                        <img src="img/logo-ministere.png" alt="Ministère des Habitants" class="college-image">
                                    </div>
                                    <div class="card-badge internship-badge">
                                        <i class="fas fa-briefcase"></i>
                                        Stage
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h3 class="degree">Stage de Développement</h3>
                                    <h4 class="specialization">Développement de Logiciel de Gestion de Frais de Mission</h4>
                                    <p class="institution">Ministère des Habitants - République Islamique de Mauritanie</p>
                                    <div class="education-details">
                                        <div class="detail-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <span>2024</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>Nouakchott, Mauritanie</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-code"></i>
                                            <span>Développement Full-Stack</span>
                                        </div>
                                    </div>
                                    <div class="skills-acquired">
                                        <h5>Projet réalisé :</h5>
                                        <div class="skill-tags">
                                            <span class="skill-tag">Application Web</span>
                                            <span class="skill-tag">Gestion des Missions</span>
                                            <span class="skill-tag">Calcul des Frais</span>
                                            <span class="skill-tag">Interface Admin</span>
                                            <span class="skill-tag">Base de Données</span>
                                        </div>
                                    </div>
                                    <div class="skills-acquired">
                                        <h5>Technologies utilisées :</h5>
                                        <div class="skill-tags">
                                            <span class="skill-tag">Django</span>
                                            <span class="skill-tag">Python</span>
                                            <span class="skill-tag">HTML/CSS</span>
                                            <span class="skill-tag">JavaScript</span>
                                            <span class="skill-tag">PostgreSQL</span>
                                            <span class="skill-tag">Bootstrap</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                  

                </div>
            </div>
        </div>
    </section>

    <!--nihayit edu-->
    <section id="contact" class="animate-on-scroll">
        <div class="container">
            <h2>
                <i class="fa-solid fa-headset"></i>Entrer en <span class="text-pimary">Contact</span>
            </h2>
            <hr>

            <!-- Section Contact avec layout 2 colonnes -->
            <div class="contact-layout">
                <!-- Informations de contact à gauche -->
                <div class="contact-info-left">
                    <h3>Contactez-moi</h3>
                    <p class="contact-description">N'hésitez pas à me contacter pour discuter de vos projets ou pour toute collaboration.</p>

                    <div class="contact-cards">
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Courriel</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Localisation</h4>
                                <p>Nouakchott, Mauritanie</p>
                            </div>
                        </div>
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Téléphone</h4>
                                <p>+222 38 49 31 49</p>
                            </div>
                        </div>
                    </div>

                    <!-- Réseaux sociaux -->
                    <div class="contact-social">
                        <h4>Suivez-moi</h4>
                        <div class="social-links">
                            <a href="#" class="social-link">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="http://wa.me/+22238493149" target="_blank" class="social-link">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-twitter"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de contact à droite -->
                <div class="contact-form-container">
                    <h3>Envoyez-moi un message</h3>
                    <form action="https://formsubmit.co/<EMAIL>" method="post" class="contact-form">
                        <div class="form-group">
                            <input type="text" name="Name" id="Name" placeholder="Votre nom" required>
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="form-group">
                            <input type="email" name="email" id="email" placeholder="Votre email" required>
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="form-group">
                            <input type="text" name="subject" id="subject" placeholder="Sujet" required>
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="form-group">
                            <textarea name="message" id="message" rows="6" placeholder="Votre message" required></textarea>
                            <i class="fas fa-comment"></i>
                        </div>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-paper-plane"></i>
                            Envoyer le message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>
     <!--===== FOOTER =====-->
     <footer class="footer">
        <p class="footer__title">Tourad Dah</p>
        <div class="social">
            <a href=""><i class="fa-brands fa-github"></i></a>
            <a href=""><i class="fa-brands fa-square-facebook"></i></a>
            <a href="" target="_blank"><i class="fa-brands fa-square-x-twitter"></i></a>
            <a href=""> <i class="fa-brands fa-linkedin"></i></a>
            <a href="http://wa.me/+22238493149" target="_blank"><i class="fa-brands fa-whatsapp"></i></a>
        </div>
        <p class="footer__copy">&#169; 2025. Tous droits réservés</p>
    </footer>
</main>

<!-- Bouton retour en haut -->
<button type="button" id="scrollToTop" class="scroll-to-top" onclick="scrollToTop()" title="Retour en haut" aria-label="Retour en haut de la page">
    <i class="fas fa-chevron-up"></i>
</button>

<script src="main.js"></script>
<script src="https://kit.fontawesome.com/f8e1a90484.js" crossorigin="anonymous"></script>
</body>
</html>