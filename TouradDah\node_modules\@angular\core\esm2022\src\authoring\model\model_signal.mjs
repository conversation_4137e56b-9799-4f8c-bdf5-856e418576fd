/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { producerAccessed, SIGNAL, signalSetFn } from '@angular/core/primitives/signals';
import { RuntimeError } from '../../errors';
import { signalAsReadonlyFn } from '../../render3/reactivity/signal';
import { ɵINPUT_SIGNAL_BRAND_READ_TYPE, ɵINPUT_SIGNAL_BRAND_WRITE_TYPE } from '../input/input_signal';
import { INPUT_SIGNAL_NODE, REQUIRED_UNSET_VALUE } from '../input/input_signal_node';
import { OutputEmitterRef } from '../output/output_emitter_ref';
/**
 * Creates a model signal.
 *
 * @param initialValue The initial value.
 *   Can be set to {@link REQUIRED_UNSET_VALUE} for required model signals.
 * @param options Additional options for the model.
 */
export function createModelSignal(initialValue) {
    const node = Object.create(INPUT_SIGNAL_NODE);
    const emitterRef = new OutputEmitterRef();
    node.value = initialValue;
    function getter() {
        producerAccessed(node);
        assertModelSet(node.value);
        return node.value;
    }
    getter[SIGNAL] = node;
    getter.asReadonly = signalAsReadonlyFn.bind(getter);
    // TODO: Should we throw an error when updating a destroyed model?
    getter.set = (newValue) => {
        if (!node.equal(node.value, newValue)) {
            signalSetFn(node, newValue);
            emitterRef.emit(newValue);
        }
    };
    getter.update = (updateFn) => {
        assertModelSet(node.value);
        getter.set(updateFn(node.value));
    };
    getter.subscribe = emitterRef.subscribe.bind(emitterRef);
    getter.destroyRef = emitterRef.destroyRef;
    if (ngDevMode) {
        getter.toString = () => `[Model Signal: ${getter()}]`;
    }
    return getter;
}
/** Asserts that a model's value is set. */
function assertModelSet(value) {
    if (value === REQUIRED_UNSET_VALUE) {
        throw new RuntimeError(-952 /* RuntimeErrorCode.REQUIRED_MODEL_NO_VALUE */, ngDevMode && 'Model is required but no value is available yet.');
    }
}
//# sourceMappingURL=data:application/json;base64,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