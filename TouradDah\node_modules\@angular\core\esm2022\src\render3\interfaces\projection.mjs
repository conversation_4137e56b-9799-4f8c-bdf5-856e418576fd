/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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